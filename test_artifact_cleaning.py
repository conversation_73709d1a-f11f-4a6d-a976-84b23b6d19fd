#!/usr/bin/env python3
"""
Test script to verify Kimi artifact cleaning functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from adk_server import clean_kimi_artifacts

def test_artifact_cleaning():
    """Test the artifact cleaning function with various Kimi artifacts."""
    
    test_cases = [
        {
            "name": "Tool call argument artifacts",
            "input": "retrieve_info_from_memory:0<|tool_call_argument_begin|>{\"query\": \"user preferences for 5 year old activities in New Westminster location\"}<|tool_call_end|>",
            "expected_clean": True
        },
        {
            "name": "Real problematic text from frontend",
            "input": "I'llfindback-to-backactivitiesfor5-year-oldsinNewWestminster.Letmesearchforactivitiestodaytoshowyouwhat'savailable.",
            "expected_clean": True  # This SHOULD be cleaned to fix spacing issues
        },
        {
            "name": "Spacing issues with mixed case",
            "input": "PerfectBack-to-BackActivitiesfor5-year-olds",
            "expected_clean": True  # Should add spaces before capitals
        },
        {
            "name": "Format markers with content",
            "input": "/*PLANNING*/1.First,retrieveanyexistinginformationabouttheuser'spreferencesandfamilycontextfrommemory2.Getthecurrentdatetounderstandwhat\"today\"means3.Searchforback-to-backactivitiesfor5-year-oldsinNewWestminsterfortoday/*ACTION*/retrieve_info_from_memory(query=\"userpreferencesandfamilydetailsfor5yearoldinNewWestminster\")",
            "expected_clean": True  # This SHOULD be cleaned to remove format markers
        },
        {
            "name": "Format markers with final answer",
            "input": "/*PLANNING*/Here's my plan/*ACTION*/tool_call()/*REASONING*/Analysis/*FINAL_ANSWER*/Here are the swimming classes for 5-year-olds in New Westminster.",
            "expected_clean": True
        },
        {
            "name": "Tool calls section artifacts",
            "input": "Let me search for activities<|tool_calls_section|>some_tool_call_here<|tool_calls_section_end|>",
            "expected_clean": True
        },
        {
            "name": "Mixed artifacts with typo",
            "input": "Planning activities<|tool_calls_sectioall_end|>more content here",
            "expected_clean": True
        },
        {
            "name": "Clean text without artifacts",
            "input": "Here are some swimming classes for 5-year-olds in New Westminster:",
            "expected_clean": False
        },
        {
            "name": "Function call artifacts",
            "input": "Processing request<|function_call|>tool_name<|function_call_end|>",
            "expected_clean": True
        },
        {
            "name": "Multiple artifacts in sequence",
            "input": "<|tool_call_start|>some_tool<|tool_call_argument_begin|>{\"arg\": \"value\"}<|tool_call_argument_end|><|tool_call_end|>",
            "expected_clean": True
        }
    ]
    
    print("🧪 Testing Kimi Artifact Cleaning Function")
    print("=" * 50)
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['name']}")
        print(f"Input: {test_case['input'][:100]}{'...' if len(test_case['input']) > 100 else ''}")
        
        cleaned = clean_kimi_artifacts(test_case['input'])
        print(f"Output: {cleaned[:100]}{'...' if len(cleaned) > 100 else ''}")
        
        # Check if cleaning was expected
        was_cleaned = cleaned != test_case['input']
        expected_clean = test_case['expected_clean']
        
        if was_cleaned == expected_clean:
            print("✅ PASS")
        else:
            print("❌ FAIL")
            print(f"   Expected cleaning: {expected_clean}, Got cleaning: {was_cleaned}")
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All tests passed! Artifact cleaning is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the cleaning logic.")
    
    return all_passed

if __name__ == "__main__":
    test_artifact_cleaning()
