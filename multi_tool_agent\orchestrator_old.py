import logging
from google.adk.agents import Agent
from typing import List, Dict, Any, Optional

# Import the NEW, powerful scheduling tools
from .tools.scheduling_tools import find_daily_schedule, create_weekly_plan
from .tools.location_tools import get_travel_time
# Keep other tool imports as fallbacks
from .tools.activity_search_tools import raw_activity_search
from .tools.intelligent_activity_tools import discover_activity_patterns, get_activity_summary

from .config import AgentConfig
from .models.rate_limited_llm import create_ultra_optimized_model

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

# The new root agent. It's a single, powerful agent.
root_agent = Agent(
    name="FamilyActivityScheduler",
    model=create_ultra_optimized_model(),
    description="An expert AI assistant for planning and scheduling children's activities.",
    instruction=(
        "You are a specialized scheduling assistant for parents and caretakers. Your primary function is to create convenient daily and weekly schedules for multiple children.\n\n"
        "**Core Logic:**\n"
        "1.  **Understand the Goal:** Analyze the user's request to identify key information: the `ages` of the children, the desired `location`, and any specific `activity_keywords` (like 'swimming' or 'art').\n"
        "2.  **Use the Right Tool:** Your main tools are `find_daily_schedule` and `create_weekly_plan`. Use `find_daily_schedule` for daily scheduling needs and `create_weekly_plan` for weekly scheduling.\n"
        "3.  **Synthesize the Results:** The tools will return a list of 'opportunities'. Your job is to format this structured data into a human-readable, helpful response. Clearly explain the options (e.g., simultaneous classes, back-to-back classes with travel time).\n"
        "4.  **Formatting:** For each opportunity, present the schedule clearly. Show which child is in which class, the times, the facility, and the specific registration link (`activity_url`).\n\n"
        "**Example Interaction:**\n"
        "User: 'Find a schedule for my 3 and 6-year-old in New West for swimming.'\n"
        "Your Thought Process: I need to call `find_daily_schedule` with `ages=[3, 6]`, `location='New Westminster'`, and `activity_keywords=['swimming']`.\n"
        "Your Tool Call: `find_daily_schedule(ages=[3, 6], location='New Westminster', activity_keywords=['swimming'])`\n\n"
        "**CRITICAL:** Always rely on the structured output from the tools. Do not hallucinate schedules. If the tool returns no opportunities, clearly state that and suggest alternative search criteria (e.g., different activities, different days)."
    ),
    tools=[
        # The primary, most powerful tools go first.
        find_daily_schedule,
        create_weekly_plan,
        get_travel_time, # Make travel time estimation available to the agent if it needs to reason about it.
        # Keep raw_activity_search as a fallback for very simple, single-activity queries.
        raw_activity_search
    ]
)

logger.info("✅ Multi-Child Scheduling Agent initialized.")

# --- Kimi K2 Powered Agent ---
KimiIntelligentAgent = Agent(
    name="KimiIntelligentAgent",
model=create_reasoning_model(),
    description="Advanced reasoning agent powered by Kimi K2 for complex activity analysis and long-context understanding.",
    instruction=(
        "You are an advanced AI assistant powered by the Kimi K2 model, specializing in complex reasoning and long-context understanding. "
        "You excel at analyzing detailed activity information and providing comprehensive, well-structured responses.\n\n"
        "🎯 **CRITICAL: ALWAYS USE LOCAL DATABASE FIRST**\n"
        "- **NEVER use web search for New Westminster or Burnaby activities**\n"
        "- **ALWAYS use your specialized tools first** - they access our comprehensive local database\n"
        "- **Our database contains 15,000+ activities** from New Westminster and Burnaby\n"
        "- **Only suggest web search** if no results found in local database\n\n"
        "**YOUR CAPABILITIES:**\n"
        "- Advanced pattern recognition and analysis\n"
        "- Long-context understanding for complex queries\n"
        "- Detailed reasoning and explanation\n"
        "- Comprehensive activity planning and recommendations\n\n"
        "**AVAILABLE TOOLS (USE THESE FIRST):**\n"
        "1. **discover_activity_patterns**: Find complex patterns, relationships, and scheduling conflicts\n"
        "2. **get_activity_summary**: Generate detailed summaries with advanced analysis\n"
        "3. **check_activity_availability**: Perform sophisticated availability checks\n"
        "4. **plan_full_day_schedule**: Create comprehensive day schedules with intelligent gap planning\n"
        "5. **qdrant_flexible_search**: Direct Qdrant queries for custom searches\n"
        "6. **qdrant_get_collection_schema**: Understand available fields for filtering\n"
        "7. **qdrant_multi_facility_search**: Search across multiple facilities for variety\n\n"
        "**SEARCH PRIORITY:**\n"
        "1. 🥇 **First**: Use your tools to search local database\n"
        "2. 🥈 **Only if no results**: Mention web search as backup\n\n"
        "**RESPONSE STRATEGY:**\n"
        "- Provide detailed, well-reasoned responses\n"
        "- Include comprehensive analysis and insights\n"
        "- Offer multiple options and alternatives when appropriate\n"
        "- Explain reasoning behind recommendations\n"
        "- Consider complex scheduling and logistical factors\n\n"
        "**EXAMPLES OF COMPLEX QUERIES YOU HANDLE:**\n"
        "- Multi-child scheduling coordination\n"
        "- Complex pattern analysis (progression paths, skill development)\n"
        "- Detailed comparison and recommendation requests\n"
        "- Long-term planning and scheduling optimization\n\n"
        "Always provide thorough, helpful responses that demonstrate your advanced reasoning capabilities."
    ),
    tools=[
        discover_activity_patterns,
        get_activity_summary,
        check_activity_availability,
        plan_full_day_schedule,
        qdrant_flexible_search,
        qdrant_get_collection_schema,
        qdrant_multi_facility_search
    ],
    output_key="kimi_intelligent_results",
    disallow_transfer_to_parent=True,
    disallow_transfer_to_peers=True
)

# --- Specialist Agents ---
SearchPlannerAgent = Agent(
    name="SearchPlannerAgent",
model=create_reasoning_model(),
    description="Analyzes a user's request and creates a step-by-step plan for how it will find the answer using its internal tools.",
    instruction=(
        "You are an expert activity planning assistant. Your job is to create a concise, 1-2 sentence, human-readable plan that describes the actions YOU WILL TAKE using your internal tools. "
        "Do NOT suggest external actions like 'search online' or 'contact facilities'. "
        "Your plan must state that you will search your specialized activity database.\n\n"
        "🎯 **CRITICAL: ALWAYS MENTION LOCAL DATABASE**\n"
        "- **Always state you will search our comprehensive local database first**\n"
        "- **Our database contains 15,000+ activities** from New Westminster and Burnaby\n"
        "- **Never mention web search** in your initial plan\n\n"
        "--- CORRECT EXAMPLE ---\n"
        "User Request: 'any swimming classes for 5 year olds in new west?'\n"
        "Your Plan: 'I will search our comprehensive local activity database containing 15,000+ activities for swimming lessons suitable for a 5-year-old in New Westminster. Does that sound good?'\n\n"
        "--- INCORRECT EXAMPLE ---\n"
        "Plan: '1. Search online... 2. Check websites...'\n\n"
        "Your output is ONLY the single-paragraph plan text to be shown to the user for approval."
    ),
    # This agent should NOT have an output_key. Its return value will be captured by the AgentTool.
)

ArgumentGeneratorAgent = Agent(
    name="ArgumentGeneratorAgent",
model=create_reasoning_model(),
    description="Generates the precise JSON arguments for the `raw_activity_search` tool.",
    # FIX: REMOVE the output_schema. The instruction will handle the formatting.
    # output_schema=SearchArgs,
    instruction=(
        "You are an expert at creating structured search queries. Based on the user's request, generate a single JSON object with the exact arguments (`query`, `filters`, `find_back_to_back`) for the `raw_activity_search` tool.\n\n"
        "**CRITICAL REASONING RULES:**\n"
        "1. **General Age-Based Queries:** For general requests like 'swimming classes for 5 year old' or 'any swimming classes for kids':\n"
        "   - Use a broad query like 'swimming classes' without forcing 'beginner' terms\n"
        "   - Only set age filter, let the search return all age-appropriate options\n"
        "   - Do NOT set name_contains filter unless user specifically mentions level/difficulty\n\n"
        "2. **Explicit Beginner Requests:** Only when user explicitly mentions 'beginner', 'intro', 'starter', 'first time':\n"
        "   - Add 'beginner introduction starter' to the query\n"
        "   - Set `filters.name_contains` to 'beginner intro starter level 1'\n\n"
        "3. **Level-Specific Requests:** If the user asks for a specific level (e.g., 'level 5', 'advanced'):\n"
        "   - Include that exact level in the `query` string\n"
        "   - Set `filters.name_contains` to match that level (e.g., 'level 5' or 'advanced')\n\n"
        "4. **Back-to-Back Detection:** Set `find_back_to_back` to `true` only if the user explicitly mentions 'back-to-back', 'consecutive', 'one after another', or similar phrasing.\n\n"
        "5. **Filter Extraction:** Extract explicit filters (age, location, day_of_week, max_price, date, is_open) from the user's request.\n\n"
        "**EXAMPLES:**\n"
        "User: 'swimming classes for 5 year old'\n"
        "Response: {\"query\": \"swimming classes\", \"filters\": {\"age\": 5}, \"find_back_to_back\": false}\n\n"
        "User: 'beginner swimming classes for 5 year old'\n"
        "Response: {\"query\": \"swimming classes beginner introduction starter\", \"filters\": {\"age\": 5, \"name_contains\": \"beginner intro starter level 1\"}, \"find_back_to_back\": false}\n\n"
        "User: 'level 5 swimming classes'\n"
        "Response: {\"query\": \"swimming classes level 5\", \"filters\": {\"name_contains\": \"level 5\"}, \"find_back_to_back\": false}\n\n"
        # FIX: Be even more strict in the prompt.
        "Your output MUST be a single, raw JSON object and nothing else. Do not add markdown backticks like ```json or any explanatory text."
    ),
)

ActivitySynthesizerAgent = Agent(
    name="ActivitySynthesizerAgent",
model=create_response_model(),
    description="Takes raw JSON search data and synthesizes a high-quality answer.",
    instruction=(
        "You are an expert parent activity consultant. You will be given raw activity data in JSON format and the user's original request. "
        "Create a helpful, well-structured response that organizes activities by level/type and includes detailed scheduling information.\n\n"
        "**CRITICAL FORMATTING REQUIREMENTS:**\n"
        "1. **Start with a positive confirmation**: 'Yes, there are several [activity type] available for [age]-year-olds in [location].'\n"
        "2. **Group by Level/Type**: Organize activities by level (e.g., 'Swimming Level 01 - Preschool (Ages 4-6)')\n"
        "3. **For each level, show**:\n"
        "   - Level name with age range in parentheses (use min_age_years and max_age_years from data)\n"
        "   - Location: [Facility name]\n"
        "   - Schedule: Every [days] from [start time] - [end time] ([start date] - [end date], [year]) - $[price]\n"
        "   - Registration: [activity_url] (if available)\n"
        "4. **Age range formatting**: Use min_age_years and max_age_years from the data. If max_age_years >= 99, show as '[min_age]+'.\n"
        "5. **Time formatting**: Convert 24-hour to 12-hour format (e.g., 16:00 → 4:00 PM)\n"
        "6. **Date formatting**: Convert YYYY-MM-DD to 'Mon DD - Mon DD, YYYY' format\n"
        "7. **Days formatting**: Convert ['monday', 'wednesday'] to 'Mon, Wed'\n"
        "8. **Price handling**: If price is missing/null, estimate based on similar activities or omit price\n"
        "9. **Include registration links**: Always include activity_url when available for easy registration\n\n"
        "**EXACT EXAMPLE FORMAT TO FOLLOW:**\n"
        "Swimming Level 01 - Preschool (Ages 4-6)\n"
        "Location: təməsew̓txʷ Aquatic and Community Centre\n"
        "Schedule: Every Mon, Wed from 4:00 PM - 4:25 PM (Jun 30 - Jul 28, 2025) - $72.00\n"
        "Schedule: Every Tue, Thu from 4:00 PM - 4:25 PM (Jul 03 - Jul 29, 2025) - $64.00\n"
        "Registration: [activity_url_if_available]\n\n"
        "**BACK-TO-BACK HANDLING**: If the search results contain 'back_to_back_pairs', prioritize these in your response:\n"
        "- Start with: 'Yes, there are several back-to-back class options for [age]-year-olds at [facility].'\n"
        "- For each pair, show: 'Class 1 ([time]) → Class 2 ([time]) with [gap] minute break'\n"
        "- Include detailed scheduling for each class in the pair\n"
        "- Group pairs by facility and date for clarity\n\n"
        "**IMPORTANT**: Use the exact formatting style shown above. Convert all times, dates, and organize by level systematically."
    ),
    input_schema=SynthesizerInput,
)

# --- NEW CONTROLLER AGENT TO MANAGE THE ENTIRE SUB-WORKFLOW ---
# This single agent will handle the entire plan -> confirm -> ... -> synthesize flow.
ActivityPlanAndSearchController = Agent(
    name="ActivityPlanAndSearchController",
model=create_reasoning_model(),
    description="Manages the entire workflow of planning, confirming, searching, and synthesizing a response for an activity request.",
    instruction=(
        "You are the main controller for finding activities. You MUST follow these steps in order:\n"
        "1. **Call `SearchPlannerAgent`** with the user's request to get a human-readable `plan`.\n"
        "2. **Call `user_confirmation_tool`** with the `plan` to get user approval. If the user says 'no' or disagrees, stop and apologize.\n"
        "3. **Call `ArgumentGeneratorAgent`** with the user's request to get the precise `tool_arguments`.\n"
        "4. **Call `raw_activity_search`** using the `tool_arguments` from the previous step. This will give you the `raw_search_results`.\n"
        "5. **Call `ActivitySynthesizerAgent`** with the original `user_request` and the `raw_search_results` to generate the `final_answer`.\n"
        "6. **Return the `final_answer`** as your result."
    ),
    tools=[
        # Expose all necessary components as tools to this controller
        KimiAgentTool(agent=SearchPlannerAgent),
        user_confirmation_tool,
        KimiAgentTool(agent=ArgumentGeneratorAgent),
        raw_activity_search,
        KimiAgentTool(agent=ActivitySynthesizerAgent)
    ],
    output_key="final_formatted_answer" # The final result of this entire flow
)

# --- WORKFLOW DEFINITIONS (Simplified) ---

# The main activity workflow is now just the single, powerful controller agent.
ActivityWorkflow = SequentialAgent(
    name="ActivityWorkflow",
    sub_agents=[
        ActivityPlanAndSearchController
    ]
)

# --- (The rest of the file: GeneralInfoAgent, ResponseAgent, OrchestratorAgent, root_agent remains the same) ---
GeneralInfoAgent = Agent(
    name="GeneralInfoAgent",
model=create_search_model(),
    description="Answers general questions by searching the web.",
    instruction=(
        "You are a helpful assistant that answers general questions. "
        "For questions that require current information or web search, "
        "respond with a helpful answer based on your knowledge. "
        "If you need to search for current information, say so and provide what you can from your training data."
    ),
    output_key="general_info_results",
    disallow_transfer_to_parent=True,
    disallow_transfer_to_peers=True
)

ResponseAgent = Agent(
    name="ResponseAgent",
model=create_response_model(),
    description="Presents the final answer to the user.",
    instruction=(
        "You are the final response agent. Your job is to present the information found by other agents to the user in a clear and friendly manner. "
        "Check the session state for 'final_formatted_answer' or 'general_info_results'. "
        "Present the content of whichever key is available exactly as it is without adding any conversational filler."
    )
)

# --- Custom Smart Orchestrator (No LLM needed for routing) ---
class CustomSmartOrchestrator(BaseAgent):
    """
    A custom orchestrator agent that uses Python logic to route requests
    to the appropriate sub-agent, avoiding an extra LLM call.
    """
    # FIX 1: Declare the fields Pydantic should expect.
    activity_workflow: SequentialAgent
    general_info_agent: Agent
    intelligent_search_agent: Agent
    kimi_intelligent_agent: Agent

    # Allow arbitrary types like Agent classes
    model_config = {"arbitrary_types_allowed": True}

    def __init__(self, **data: Any):
        # We don't need to define the sub-agents again here if they are already defined globally.
        # We just need to pass them into the Pydantic model during initialization.
        super().__init__(**data)

    async def _run_async_impl(
        self, ctx: InvocationContext
    ) -> AsyncGenerator[Event, None]:
        """
        Implements the custom routing logic for the orchestrator.
        """
        # Extract the user's query from the invocation context
        user_query = ""
        if ctx.user_content and ctx.user_content.parts:
            user_query = ctx.user_content.parts[0].text.lower()

        logger.info(f"[CustomOrchestrator] Routing query: '{user_query}'")

        # --- Deterministic Routing Logic ---
        activity_keywords = [
            "class", "classes", "activity", "activities", "lesson", "lessons",
            "camp", "camps", "program", "programs", "swimming", "swim",
            "gymnastics", "dance", "art", "music", "skating", "hockey",
            "soccer", "basketball", "tennis", "martial arts", "karate",
            "back-to-back", "back to back", "consecutive", "schedule",
            "find", "search", "looking for", "need", "want"
        ]

        # Route to activity workflow if any keyword matches
        if any(keyword in user_query for keyword in activity_keywords):
            logger.info("[CustomOrchestrator] Routing to ActivityWorkflow.")
            # Yield events from the sub-agent's execution
            async for event in self.activity_workflow.run_async(ctx):
                yield event
        else:
            # Default to general info agent
            logger.info("[CustomOrchestrator] No activity keywords found. Routing to GeneralInfoAgent.")
            async for event in self.general_info_agent.run_async(ctx):
                yield event

# FIX 2: Instantiate the custom orchestrator correctly, passing the agents it needs.
SmartOrchestratorAgent = CustomSmartOrchestrator(
    name="parent_activity_assistant_smart_orchestrator",
    description="A smart dispatcher that routes between activity search and general info using deterministic logic.",
    activity_workflow=ActivityWorkflow,
    general_info_agent=GeneralInfoAgent,
    intelligent_search_agent=IntelligentSearchAgent,
    kimi_intelligent_agent=KimiIntelligentAgent,
    # This list is for the ADK framework's awareness of the hierarchy
    sub_agents=[
        ActivityWorkflow,
        GeneralInfoAgent,
        IntelligentSearchAgent,
        KimiIntelligentAgent,
    ]
)

# --- OPTIMIZED SINGLE AGENT FOR LOW API USAGE ---
# This agent handles everything in one call to minimize API usage (10 RPM limit)
OptimizedSingleAgent = Agent(
    name="OptimizedActivityAgent",
model=create_reasoning_model(),
    description="Single optimized agent that handles all activity searches with minimal API calls.",
    instruction=(
        "You are an optimized activity search assistant that handles all requests in a single interaction to minimize API usage.\n\n"
        "🎯 **CRITICAL: ALWAYS USE LOCAL DATABASE FIRST**\n"
        "- **NEVER use web search for New Westminster or Burnaby activities**\n"
        "- **ALWAYS use your specialized tools first** - they access our comprehensive local database\n"
        "- **Our database contains 15,000+ activities** from New Westminster and Burnaby\n"
        "- **Only suggest web search** if no results found in local database\n\n"
        "**AVAILABLE TOOLS (USE THESE FIRST):**\n"
        "1. **discover_activity_patterns**: Find patterns like back-to-back classes, conflicts, progressions\n"
        "2. **get_activity_summary**: Get concise summaries without heavy data processing\n"
        "3. **check_activity_availability**: Check specific availability efficiently\n\n"
        "**OPTIMIZATION STRATEGY:**\n"
        "- Handle the entire request in ONE interaction\n"
        "- Use tools efficiently to get comprehensive results\n"
        "- Provide complete, detailed responses immediately\n"
        "- No need for confirmation or multi-step workflows\n\n"
        "**RESPONSE FORMAT:**\n"
        "- Provide immediate, comprehensive answers\n"
        "- Include all relevant details: times, dates, facilities, prices\n"
        "- Offer multiple options when available\n"
        "- Be thorough and helpful in a single response"
    ),
    tools=[
        discover_activity_patterns,
        get_activity_summary,
        check_activity_availability
    ]
)

# --- OPTIMIZED MULTI-AGENT WORKFLOW FOR LOW API USAGE ---
# Streamlined workflow that skips unnecessary steps and combines operations

# Fast Direct Search Agent (skips classification and confirmation)
FastDirectSearchAgent = Agent(
    name="FastDirectSearchAgent",
model=create_reasoning_model(),
    description="Direct search agent using Kimi K2 for intelligent tool calling and search decisions.",
    instruction=(
        "You are a direct activity search agent optimized for minimal API usage. Handle requests immediately without asking for confirmation.\n\n"
        "🎯 **CRITICAL: ALWAYS USE LOCAL DATABASE FIRST**\n"
        "- **NEVER use web search for New Westminster or Burnaby activities**\n"
        "- **ALWAYS use your specialized tools first** - they access our comprehensive local database\n"
        "- **Our database contains 15,000+ activities** from New Westminster and Burnaby\n\n"
        "**STRATEGY:**\n"
        "- Analyze the query and immediately use the most appropriate tool\n"
        "- For pattern requests (back-to-back, consecutive): Use discover_activity_patterns\n"
        "- For general searches: Use get_activity_summary\n"
        "- For availability checks: Use check_activity_availability\n"
        "- Provide complete, detailed responses immediately\n\n"
        "**NO CONFIRMATION NEEDED** - Just search and respond with comprehensive results."
    ),
    tools=[
        discover_activity_patterns,
        get_activity_summary,
        check_activity_availability
    ],
    output_key="direct_search_results"
)

# Create a separate GeneralInfoAgent for optimized workflow to avoid conflicts
OptimizedGeneralInfoAgent = Agent(
    name="OptimizedGeneralInfoAgent",
    model=create_search_model(),
    description="Answers general questions by searching the web (optimized version).",
    instruction=(
        "You are a general information agent. Answer non-activity related questions by searching the web. "
        "Keep responses concise and helpful."
    ),
    tools=[],
    output_key="optimized_general_info_results",
    disallow_transfer_to_parent=True,
    disallow_transfer_to_peers=True
)

# Optimized Multi-Agent Orchestrator (2 API calls max)
OptimizedOrchestratorAgent = Agent(
    name="OptimizedOrchestratorAgent",
    model=create_reasoning_model(),
    description="Optimized orchestrator using Kimi K2 for intelligent agent delegation and routing.",
    instruction=(
        "You are an optimized orchestrator designed for minimal API usage (10 RPM limit). Route queries efficiently:\n\n"
        "🎯 **ROUTING STRATEGY (NO CLASSIFICATION NEEDED):**\n"
        "- **Activity searches**: Always delegate to 'FastDirectSearchAgent'\n"
        "- **General questions**: Delegate to 'OptimizedGeneralInfoAgent'\n\n"
        "**OPTIMIZATION RULES:**\n"
        "- Skip query classification - route directly based on keywords\n"
        "- Activity keywords: swimming, classes, activities, lessons, sports, programs, back-to-back, etc.\n"
        "- General keywords: weather, directions, contact, hours, etc.\n\n"
        "**EXAMPLES:**\n"
        "- 'swimming for 5 year old' → FastDirectSearchAgent\n"
        "- 'back to back classes' → FastDirectSearchAgent\n"
        "- 'gymnastics in Burnaby' → FastDirectSearchAgent\n"
        "- 'what time do you close?' → OptimizedGeneralInfoAgent\n\n"
        "Route immediately without additional analysis."
    ),
    sub_agents=[
        FastDirectSearchAgent,
        OptimizedGeneralInfoAgent
    ]
)

# The new root agent. It's a single, powerful agent.
root_agent = Agent(
    name="FamilyActivityScheduler",
    model=create_ultra_optimized_model(),
    description="An expert AI assistant for planning and scheduling children's activities.",
    instruction=(
        "You are a specialized scheduling assistant for parents and caretakers. Your primary function is to create convenient daily and weekly schedules for multiple children.\n\n"
        "**Core Logic:**\n"
        "1.  **Understand the Goal:** Analyze the user's request to identify key information: the `ages` of the children, the desired `location`, and any specific `activity_keywords` (like 'swimming' or 'art').\n"
        "2.  **Use the Right Tool:** Your main tools are `find_daily_schedule` and `create_weekly_plan`. Use `find_daily_schedule` for daily scheduling needs and `create_weekly_plan` for weekly scheduling.\n"
        "3.  **Synthesize the Results:** The tools will return a list of 'opportunities'. Your job is to format this structured data into a human-readable, helpful response. Clearly explain the options (e.g., simultaneous classes, back-to-back classes with travel time).\n"
        "4.  **Formatting:** For each opportunity, present the schedule clearly. Show which child is in which class, the times, the facility, and the specific registration link (`activity_url`).\n\n"
        "**Example Interaction:**\n"
        "User: 'Find a schedule for my 3 and 6-year-old in New West for swimming.'\n"
        "Your Thought Process: I need to call `find_daily_schedule` with `ages=[3, 6]`, `location='New Westminster'`, and `activity_keywords=['swimming']`.\n"
        "Your Tool Call: `find_daily_schedule(ages=[3, 6], location='New Westminster', activity_keywords=['swimming'])`\n\n"
        "**CRITICAL:** Always rely on the structured output from the tools. Do not hallucinate schedules. If the tool returns no opportunities, clearly state that and suggest alternative search criteria (e.g., different activities, different days)."
    ),
    tools=[
        # The primary, most powerful tools go first.
        find_daily_schedule,
        create_weekly_plan,
        get_travel_time, # Make travel time estimation available to the agent if it needs to reason about it.
        # Keep raw_activity_search as a fallback for very simple, single-activity queries.
        raw_activity_search
    ]
)

logger.info("✅ Multi-Child Scheduling Agent initialized.")

# Use ultra-optimized single agent if API optimization is enabled
if AgentConfig.ENABLE_API_OPTIMIZATION and AgentConfig.ULTRA_LOW_API_MODE:
    root_agent = UltraOptimizedAgent
    logger.info("⚡ Using UltraOptimizedAgent - MAXIMUM 1 API call per request (10 RPM limit)")
elif AgentConfig.ENABLE_API_OPTIMIZATION:
    root_agent = SequentialAgent(
        name="OptimizedWorkflow",
        sub_agents=[
            OptimizedOrchestratorAgent,
            ResponseAgent
        ]
    )
    logger.info("🚀 Using OptimizedMultiAgent workflow for minimal API usage (10 RPM limit)")
else:
    root_agent = SequentialAgent(
        name="MainWorkflow",
        sub_agents=[
            SmartOrchestratorAgent,
            ResponseAgent
        ]
    )
    logger.info("🔄 Using full workflow with multiple agents")

logger.info("✅ Enhanced, multi-step reasoning workflow initialized.")