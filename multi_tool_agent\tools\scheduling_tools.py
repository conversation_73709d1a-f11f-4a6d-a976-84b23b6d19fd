# multi_tool_agent/tools/scheduling_tools.py

import logging
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, time as dt_time, timedelta

from .activity_search_tools import raw_activity_search
from .location_tools import get_travel_time
import re

logger = logging.getLogger(__name__)

def _to_time(time_str: str) -> Optional[dt_time]:
    """Safely convert 'HH:MM:SS' string to a time object."""
    if not time_str: return None
    try: return dt_time.fromisoformat(time_str)
    except (ValueError, TypeError): return None
    
# --- NEW, MORE ADVANCED SCHEDULING FUNCTION ---
def find_simultaneous_class_windows(activities: List[Dict], ages: List[int]) -> List[Dict]:
    """
    Finds opportunities for multiple children (of different ages) to take classes
    at the same time at the same facility.
    """
    if not ages or len(ages) < 2:
        logger.info("Need at least two ages to find simultaneous class windows.")
        return []

    # 1. Separate activities by which child can attend them
    activities_by_age = {age: [] for age in ages}
    for activity in activities:
        min_age, max_age = activity.get("min_age_years", 0), activity.get("max_age_years", 99)
        for age in ages:
            if min_age <= age <= max_age:
                activities_by_age[age].append(activity)

    # Use the first child as the "primary" anchor for scheduling
    primary_age = ages[0]
    other_ages = ages[1:]
    
    scheduling_windows = []

    # 2. Iterate through each activity for the primary child
    for primary_activity in activities_by_age[primary_age]:
        primary_start = _to_time(primary_activity.get("start_time_iso"))
        primary_end = _to_time(primary_activity.get("end_time_iso"))
        primary_facility = primary_activity.get("facility")
        primary_days = set(primary_activity.get("days_of_week_list", []))

        if not all([primary_start, primary_end, primary_facility, primary_days]):
            continue

        simultaneous_options = {age: [] for age in other_ages}
        
        # 3. For each other child, find classes that overlap in time and location
        for other_age in other_ages:
            for other_activity in activities_by_age[other_age]:
                other_start = _to_time(other_activity.get("start_time_iso"))
                other_end = _to_time(other_activity.get("end_time_iso"))
                other_facility = other_activity.get("facility")
                other_days = set(other_activity.get("days_of_week_list", []))
                
                if not all([other_start, other_end, other_facility, other_days]):
                    continue

                # Check for same facility and at least one common day
                if primary_facility == other_facility and primary_days.intersection(other_days):
                    # Check for time overlap: (StartA <= EndB) and (StartB <= EndA)
                    if primary_start <= other_end and other_start <= primary_end:
                        simultaneous_options[other_age].append(other_activity)
        
        # 4. If we found options for ALL other children, it's a valid window
        if all(simultaneous_options.values()):
            scheduling_windows.append({
                "type": "simultaneous_window",
                "primary_child_age": primary_age,
                "primary_class": primary_activity,
                "simultaneous_options_by_age": simultaneous_options,
                "facility": primary_facility,
                "common_days": list(primary_days.intersection(*(set(opt.get("days_of_week_list", [])) for age_opts in simultaneous_options.values() for opt in age_opts)))
            })
            
    return scheduling_windows

# **NEW: Compatibility validation logic**
def _validate_activity_compatibility(first_activity: Dict, second_activity: Dict) -> bool:
    """
    Validates that activities can be taken together in a day.
    Rules:
    1. A child can only take ONE level of a specific activity type.
    2. Different activity types are always compatible.
    3. Non-leveled activities can be combined with anything.
    """
    def extract_activity_info(activity_name: str) -> tuple[str, int]:
        """Extract activity type and level from name like 'Swimming Level 02' -> ('Swimming', 2)"""
        if not activity_name: return "", 0
        level_match = re.search(r'Level\s+(\d+)', activity_name, re.IGNORECASE)
        level = int(level_match.group(1)) if level_match else 0
        
        # Extract activity type (everything before 'Level')
        if level > 0:
            activity_type = re.sub(r'\s+Level\s+\d+.*', '', activity_name, flags=re.IGNORECASE).strip()
        else:
            activity_type = activity_name.strip()
        return activity_type, level

    first_name = first_activity.get('name', '')
    second_name = second_activity.get('name', '')

    if first_name == second_name and first_activity.get('start_time_iso') == second_activity.get('start_time_iso'):
        return False # Cannot book the same class twice

    first_type, first_level = extract_activity_info(first_name)
    second_type, second_level = extract_activity_info(second_name)

    # If both are leveled classes of the SAME activity type - NOT ALLOWED
    if (first_type and first_type == second_type and first_level > 0 and second_level > 0):
        logger.debug(f"Invalid pair: Cannot take two levels of '{first_type}' at once.")
        return False

    return True

def _create_schedule_opportunities(all_activities: List[Dict]) -> List[Dict]:
    """
    Core logic to find scheduling opportunities by comparing every activity with every other activity.
    This now handles cross-facility planning by incorporating travel time.
    """
    opportunities = []
    # --- FIX: Define a realistic maximum wait time in minutes ---
    MAX_WAIT_MINUTES = 180  # 3 hours

    # Create pairs of all activities to compare them
    for i in range(len(all_activities)):
        for j in range(i + 1, len(all_activities)):
            act1 = all_activities[i]
            act2 = all_activities[j]

            # Basic validation
            if act1.get("record_id") == act2.get("record_id"):
                continue # Don't compare an activity to itself

            if not _validate_activity_compatibility(act1, act2):
                continue

            # Time and Day validation
            act1_start, act1_end = _to_time(act1.get("start_time_iso")), _to_time(act1.get("end_time_iso"))
            act2_start, act2_end = _to_time(act2.get("start_time_iso")), _to_time(act2.get("end_time_iso"))
            
            if not all([act1_start, act1_end, act2_start, act2_end]):
                continue

            common_days = set(act1.get("days_of_week_list", [])) & set(act2.get("days_of_week_list", []))
            if not common_days:
                continue

            # Travel Time Calculation
            facility1, facility2 = act1.get("facility"), act2.get("facility")
            travel_minutes = get_travel_time(facility1, facility2)
            if travel_minutes is None:
                # If we can't calculate travel, we can't schedule between these facilities.
                continue

            # Use a dummy date to perform time arithmetic
            dummy_date = datetime(2024, 1, 1)
            act1_start_dt, act1_end_dt = datetime.combine(dummy_date, act1_start), datetime.combine(dummy_date, act1_end)
            act2_start_dt, act2_end_dt = datetime.combine(dummy_date, act2_start), datetime.combine(dummy_date, act2_end)

            # Check for scheduling compatibility (is there enough time between activities?)
            # Case 1: act1 ends before act2 starts
            if act1_end_dt <= act2_start_dt:
                gap_minutes = (act2_start_dt - act1_end_dt).total_seconds() / 60
                # --- FIX: Enforce maximum realistic gap ---
                if travel_minutes <= gap_minutes <= MAX_WAIT_MINUTES:
                    opportunities.append({
                        "type": "back_to_back",
                        "first_class": act1,
                        "second_class": act2,
                        "gap_minutes": int(gap_minutes),
                        "travel_time_minutes": travel_minutes,
                        "days": list(common_days),
                    })
            
            # Case 2: act2 ends before act1 starts
            elif act2_end_dt <= act1_start_dt:
                gap_minutes = (act1_start_dt - act2_end_dt).total_seconds() / 60
                # --- FIX: Enforce maximum realistic gap ---
                if travel_minutes <= gap_minutes <= MAX_WAIT_MINUTES:
                    opportunities.append({
                        "type": "back_to_back",
                        "first_class": act2,
                        "second_class": act1,
                        "gap_minutes": int(gap_minutes),
                        "travel_time_minutes": travel_minutes,
                        "days": list(common_days),
                    })

            # Case 3: Activities overlap in time (simultaneous classes)
            elif (act1_start_dt <= act2_end_dt and act2_start_dt <= act1_end_dt):
                # Check if they're at the same facility (parents can drop off multiple kids)
                if facility1 == facility2:
                    opportunities.append({
                        "type": "simultaneous",
                        "first_class": act1,
                        "second_class": act2,
                        "gap_minutes": 0,
                        "travel_time_minutes": 0,
                        "days": list(common_days),
                    })

    return opportunities

async def find_daily_schedule(
    query: str,
    age: int,
    location: str,
    date: str,
) -> Dict[str, Any]:
    """
    **Use this tool for SINGLE-DAY scheduling requests.**
    
    Perfect for queries like:
    - "Find activities for today"
    - "What's available tomorrow?" 
    - "Schedule for July 25th, 2025"
    
    This tool searches for activities on one specific date and finds scheduling
    opportunities (back-to-back classes, simultaneous classes for siblings, etc.).

    Args:
        query: Activity search terms (e.g., 'swimming', 'dance classes', 'art')
        age: The child's age in years
        location: City or area (e.g., 'New Westminster', 'Burnaby')
        date: Specific date in YYYY-MM-DD format (e.g., '2025-07-25')
    """
    logger.info(f"📅 Starting DAILY schedule search for age {age} in {location} for query: '{query}' on {date}")
    
    filters_dict = {"location": location, "age": [age], "date": date}

    search_result = await raw_activity_search(
        query=query, filters=filters_dict
    )

    if search_result.get("status") != "success" or not search_result.get("results"):
        return {"status": "success", "summary": f"No activities found for {date} matching the criteria.", "opportunities": []}

    all_activities = search_result["results"]
    opportunities = _create_schedule_opportunities(all_activities)

    summary = f"Found {len(opportunities)} potential daily scheduling opportunities for a {age}-year-old in {location} on {date}."

    return {
        "status": "success",
        "summary": summary,
        "opportunities": opportunities[:20]
    }

async def create_weekly_plan(
    query: str,
    ages: List[int],
    location: str,
    days_of_week: Optional[List[str]] = None,
) -> Dict[str, Any]:
    """
    **Use this tool for MULTI-DAY or WEEKLY scheduling requests.**
    
    Perfect for queries like:
    - "Find activities on weekdays"
    - "What's available this weekend?"
    - "Schedule for every Tuesday and Thursday"
    - "Plan a full week of activities"
    
    This tool searches for activities across multiple days and organizes them
    by day of the week. Great for planning recurring weekly schedules.

    Args:
        query: Activity search terms (e.g., 'swimming', 'gymnastics', 'art classes')
        ages: List of children's ages (e.g., [5, 8] for a 5-year-old and 8-year-old)
        location: City or area (e.g., 'New Westminster', 'Burnaby')
        days_of_week: Specific days to search (e.g., ['monday', 'wednesday']). 
                     If None, searches all 7 days of the week.
    """
    if not isinstance(ages, list) or not ages:
        return {"status": "error", "message": "This tool requires a list of at least one age."}
        
    logger.info(f"📅 Starting WEEKLY schedule creation for ages {ages} in {location} for days: {days_of_week}")
    
    # If no specific days are given, plan for the whole upcoming week.
    if not days_of_week:
        days_of_week = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]

    weekly_schedule = {day.capitalize(): {"opportunities": []} for day in days_of_week}
    total_opportunities = 0

    # Perform activity search based on days of week
    filters_dict = {"location": location, "age": ages, "day_of_week": [day.lower() for day in days_of_week]}

    search_result = await raw_activity_search(
        query=query,
        filters=filters_dict
    )

    if search_result.get("status") != "success" or not search_result.get("results"):
        return {"status": "success", "summary": "No activities found matching the criteria for the specified days.", "weekly_schedule": weekly_schedule}

    all_activities = search_result["results"]

    for activity in all_activities:
        activity_days = activity.get("days_of_week_list", [])
        for day in activity_days:
            if day.lower() in days_of_week:
                weekly_schedule[day.capitalize()]['opportunities'].append(activity)

    total_opportunities = sum(len(v['opportunities']) for v in weekly_schedule.values())
    summary = f"Created a weekly plan with {total_opportunities} total opportunities for children aged {', '.join(map(str, ages))} in {location} on {', '.join(days_of_week)}."

    return {
        "status": "success",
        "summary": summary,
        "weekly_schedule": weekly_schedule
    }
