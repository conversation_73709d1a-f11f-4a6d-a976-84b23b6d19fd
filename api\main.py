# api/main.py
import os
from fastapi import FastAPI, HTTPException, Request
from pydantic import BaseModel
from graphiti_core import Graphiti
from graphiti_core.llm_client.gemini_client import GeminiClient, LLMConfig
from graphiti_core.embedder.gemini import <PERSON><PERSON>mbedder, GeminiEmbedderConfig
from graphiti_core.cross_encoder.gemini_reranker_client import GeminiRerankerClient
from dotenv import load_dotenv
from .cache_management import (
    list_active_caches,
    get_cache_details,
    delete_cache,
    get_cache_usage_stats,
    get_session_cache_mappings
)

load_dotenv()

app = FastAPI(title="Graphiti Memory API with Explicit Caching", version="1.0.0")

def get_graphiti_client():
    # Initialize the Graphiti client using environment variables with Google Gemini
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        raise ValueError("GOOGLE_API_KEY environment variable is required")
    
    return Graphiti(
        uri=os.getenv("NEO4J_URI", "bolt://localhost:7687"),
        user=os.getenv("NEO4J_USER", "neo4j"),
        password=os.getenv("NEO4J_PASSWORD", "password"),
        llm_client=GeminiClient(
            config=LLMConfig(
                api_key=api_key,
                model=os.getenv("GRAPHITI_MODEL", "gemini-2.0-flash")
            )
        ),
        embedder=GeminiEmbedder(
            config=GeminiEmbedderConfig(
                api_key=api_key,
                embedding_model=os.getenv("EMBEDDING_MODEL", "embedding-001")
            )
        ),
        cross_encoder=GeminiRerankerClient(
            config=LLMConfig(
                api_key=api_key,
                model="gemini-2.5-flash-lite-preview-06-17"
            )
        )
    )

class PromotionRequest(BaseModel):
    names: list[str]

@app.post("/propose_concept")
async def propose_concept(request: Request):
    graphiti_client = get_graphiti_client()
    user_id = request.headers.get("X-User-ID")
    if not user_id:
        raise HTTPException(status_code=400, detail="X-User-ID header is required.")
    payload = await request.json()
    concept_name = payload.get("name")
    category_guess = payload.get("category_guess")

    query = """
    MERGE (p:ProposedConcept {name: $name})
    ON CREATE SET p.category_guess = $category, p.status = 'pending', p.proposer_id = $user_id, p.count = 1, p.created_at = datetime()
    ON MATCH SET p.count = p.count + 1
    RETURN p.name as name, p.count as count
    """
    await graphiti_client.driver.execute_query(
        query, 
        params={"name": concept_name, "category": category_guess, "user_id": user_id}
    )
    return {"message": f"Thank you! Your interest in '{concept_name}' has been noted."}

@app.get("/admin/pending_concepts")
async def get_pending_concepts(request: Request):
    graphiti_client = request.app.state.graphiti_client
    query = """
    MATCH (p:ProposedConcept {status: 'pending'})
    RETURN p.name AS name, p.category_guess AS category, p.count AS count
    ORDER BY p.count DESC
    LIMIT 100
    """
    records, _, _ = await graphiti_client.driver.execute_query(query)
    return {"concepts": [dict(record) for record in records]}

@app.post("/admin/promote_concepts")
async def promote_concepts(request: Request, payload: PromotionRequest):
    graphiti_client = request.app.state.graphiti_client
    tx_query = """
    UNWIND $names AS concept_name
    MERGE (i:Interest {name: concept_name})
    WITH i, concept_name
    MATCH (p:ProposedConcept {name: concept_name})
    SET p.status = 'approved', p.reviewed_at = datetime()
    WITH i, p
    MATCH (u:Entity {group_id: p.proposer_id})
    MERGE (u)-[:HAS_INTEREST]->(i)
    """
    await graphiti_client.driver.execute_query(tx_query, params={"names": payload.names})
    return {"status": "success", "promoted_count": len(payload.names)}

@app.post("/add_episode")
async def add_episode(request: Request):
    """Add an episode to the Graphiti knowledge graph."""
    graphiti_client = get_graphiti_client()
    payload = await request.json()
    episode_data = payload.get("episode")
    group_id = payload.get("group_id")
    
    if not episode_data or not group_id:
        raise HTTPException(status_code=400, detail="Episode data and group_id are required")
    
    try:
        from datetime import datetime
        # Parse the reference time
        reference_time_str = episode_data.get("reference_time")
        reference_time = datetime.fromisoformat(reference_time_str.replace('Z', '+00:00'))
        
        # Create episode using Graphiti's add_episode method with correct parameters
        result = await graphiti_client.add_episode(
            name=episode_data.get("name"),
            episode_body=episode_data.get("content"),  # Note: 'content' -> 'episode_body'
            source_description=episode_data.get("source_description"),
            reference_time=reference_time,
            group_id=group_id
        )
        return {"status": "success", "episode_id": result.episode.uuid}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to add episode: {str(e)}")

@app.get("/search")
async def search_memory(request: Request):
    """Search the Graphiti knowledge graph for relevant facts."""
    graphiti_client = get_graphiti_client()
    query = request.query_params.get("query", "")
    group_ids = request.query_params.getlist("group_ids")
    
    if not query:
        raise HTTPException(status_code=400, detail="Query parameter is required")
    
    try:
        # Perform search using the correct method signature
        results = await graphiti_client.search(
            query=query,
            group_ids=group_ids if group_ids else None,
            num_results=15
        )
        
        # Extract facts from results (results is a list of EntityEdge objects)
        facts = []
        for edge in results:
            if hasattr(edge, 'fact') and edge.fact:
                facts.append({
                    "fact": edge.fact,
                    "valid_at": edge.valid_at.isoformat() if hasattr(edge, 'valid_at') else None
                })
        
        return {"facts": facts}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

# ===== CACHE MANAGEMENT ENDPOINTS =====

@app.get("/admin/caches")
async def get_active_caches():
    """List all active Gemini Cache objects."""
    return await list_active_caches()

@app.get("/admin/caches/{cache_name}")
async def get_cache_info(cache_name: str):
    """Get detailed information about a specific cache."""
    return await get_cache_details(cache_name)

@app.delete("/admin/caches/{cache_name}")
async def delete_cache_endpoint(cache_name: str):
    """Delete a specific cache."""
    return await delete_cache(cache_name)

@app.get("/admin/cache-stats")
async def get_cache_stats():
    """Get usage statistics for all active caches."""
    return await get_cache_usage_stats()

@app.get("/admin/session-mappings")
async def get_session_mappings():
    """Get current session to cache mappings."""
    return get_session_cache_mappings()

