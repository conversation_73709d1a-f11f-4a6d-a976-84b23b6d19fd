#!/usr/bin/env python3
"""
Test script to debug the RAG pipeline with gymnastics query for ages 3 and 6
to verify the updated data ingestion is working correctly.
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.activity_search_tools import raw_activity_search
from tools.scheduling_tools import find_simultaneous_class_windows
from google.adk.tools import ToolContext

async def test_gymnastics_search():
    """Test the RAG pipeline with gymnastics query for ages 3 and 6"""
    
    # Mock tool context (we don't need actual ADK context for this test)
    class MockToolContext:
        def __init__(self):
            self.actions = None
    
    tool_context = MockToolContext()
    
    # Test query and filters
    query = "gymnastics classes for my kids"
    filters = {
        "age": [3, 6],
        "location": "New Westminster"
    }
    
    print("🔍 Testing RAG pipeline with gymnastics query...")
    print(f"Query: '{query}'")
    print(f"Filters: {filters}")
    print("=" * 60)
    
    try:
        # Run the search with back-to-back enabled
        results = await raw_activity_search(
            query=query,
            filters=filters,
            tool_context=tool_context,
            find_back_to_back=True
        )
        
        print(f"✅ Search completed successfully!")
        print(f"Status: {results.get('status')}")
        
        if results.get('status') == 'success':
            activities = results.get('results', [])
            back_to_back_pairs = results.get('back_to_back_pairs', [])
            
            print(f"📊 Found {len(activities)} individual activities")
            print(f"🔗 Found {len(back_to_back_pairs)} back-to-back pairs")
            
            # Show sample activities
            print("\n📋 Sample activities:")
            for i, activity in enumerate(activities[:5]):  # Show first 5
                name = activity.get('name', 'Unknown')
                min_age = activity.get('min_age_years', 'N/A')
                max_age = activity.get('max_age_years', 'N/A')
                start_time = activity.get('start_time_iso', 'N/A')
                end_time = activity.get('end_time_iso', 'N/A')
                days = activity.get('days_of_week_list', [])
                facility = activity.get('facility', 'N/A')
                
                print(f"  {i+1}. {name}")
                print(f"     Age: {min_age}-{max_age} years")
                print(f"     Time: {start_time} - {end_time}")
                print(f"     Days: {', '.join(days) if days else 'N/A'}")
                print(f"     Facility: {facility}")
                print()
            
            # Show back-to-back pairs
            print("🔗 Back-to-back pairs:")
            for i, pair in enumerate(back_to_back_pairs[:3]):  # Show first 3
                first_class = pair.get('first_class', {})
                second_class = pair.get('second_class', {})
                gap_minutes = pair.get('time_gap_minutes', 0)
                gap_desc = pair.get('gap_description', 'N/A')
                
                print(f"  {i+1}. {first_class.get('name', 'Unknown')} → {second_class.get('name', 'Unknown')}")
                print(f"     Gap: {gap_minutes} minutes ({gap_desc})")
                print(f"     Facility: {pair.get('facilities', 'N/A')}")
                print()
            
            # Test simultaneous class windows
            print("🎯 Testing simultaneous class windows...")
            windows = find_simultaneous_class_windows(activities, [3, 6])
            print(f"Found {len(windows)} simultaneous windows")
            
            for i, window in enumerate(windows[:3]):  # Show first 3
                primary_class = window.get('primary_class', {})
                primary_age = window.get('primary_child_age', 'N/A')
                facility = window.get('facility', 'N/A')
                common_days = window.get('common_days', [])
                
                print(f"  {i+1}. Primary class for {primary_age}-year-old: {primary_class.get('name', 'Unknown')}")
                print(f"     Facility: {facility}")
                print(f"     Common days: {', '.join(common_days) if common_days else 'N/A'}")
                
                # Show options for other ages
                options = window.get('simultaneous_options_by_age', {})
                for age, age_options in options.items():
                    print(f"     Options for {age}-year-old:")
                    for opt in age_options[:2]:  # Show first 2 options
                        print(f"       - {opt.get('name', 'Unknown')}")
                print()
            
        else:
            print(f"❌ Search failed: {results.get('message', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Error during search: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_gymnastics_search())
