# multi_tool_agent/utils/time_intent.py
import re
from enum import Enum
from typing import List, Optional, Tuple

class TemporalIntent(Enum):
    EXACT_DATE = "EXACT_DATE"
    RELATIVE_DATE = "RELATIVE_DATE"
    WEEKDAY_RANGE = "WEEKDAY_RANGE"
    WEEKEND = "WEEKEND"
    NONE = "NONE"

# Comprehensive regex patterns
WEEKDAY_PATTERNS = [
    r'\bweekdays?\b',
    r'\b(mon|tues|wed|thurs|fri)day(s)?\b',
    r'\bevery\s+(monday|tuesday|wednesday|thursday|friday)\b',
    r'\bon\s+weekdays\b',
    r'\bduring\s+the\s+week\b'
]

WEEKEND_PATTERNS = [
    r'\bweekends?\b', 
    r'\b(sat|sun)(urday|day)(s)?\b', 
    r'\bthis\s+weekend\b',
    r'\bevery\s+weekend\b',
    r'\bsaturday(s)?\b',
    r'\bsunday(s)?\b'
]

RELATIVE_PATTERNS = [
    r'\btoday\b', 
    r'\btomorrow\b', 
    r'\bnext\s+week\b', 
    r'\bthis\s+week\b',
    r'\byesterday\b'
]

def classify_temporal_intent(query: str) -> TemporalIntent:
    """
    Classifies the temporal intent of a user query using robust regex.
    
    Args:
        query: The user's natural language query.

    Returns:
        A TemporalIntent enum value.
    """
    lower_query = query.lower()
    
    # Check for specific date format first (YYYY-MM-DD)
    if re.search(r'\d{4}-\d{2}-\d{2}', lower_query):
        return TemporalIntent.EXACT_DATE
        
    # Check weekday patterns
    for pattern in WEEKDAY_PATTERNS:
        if re.search(pattern, lower_query):
            return TemporalIntent.WEEKDAY_RANGE
            
    # Check weekend patterns
    for pattern in WEEKEND_PATTERNS:
        if re.search(pattern, lower_query):
            return TemporalIntent.WEEKEND
            
    # Check relative date patterns
    for pattern in RELATIVE_PATTERNS:
        if re.search(pattern, lower_query):
            return TemporalIntent.RELATIVE_DATE
            
    return TemporalIntent.NONE

def get_weekday_list() -> List[str]:
    """Returns the list of weekdays for API calls."""
    return ["monday", "tuesday", "wednesday", "thursday", "friday"]

def get_weekend_list() -> List[str]:
    """Returns the list of weekend days for API calls."""
    return ["saturday", "sunday"]

def get_all_days_list() -> List[str]:
    """Returns all days of the week."""
    return get_weekday_list() + get_weekend_list()
