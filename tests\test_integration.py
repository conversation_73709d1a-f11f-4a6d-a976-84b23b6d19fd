import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from multi_tool_agent.tools.intent_tools import TemporalIntentClassifierTool
from multi_tool_agent.tools.activity_search_tools import raw_activity_search
from multi_tool_agent.utils.keyword_expansion import expand_keywords
from multi_tool_agent.utils.time_intent import TemporalIntent


class TestIntegration:
    """Integration tests for Phase 1 and Phase 2 functionality"""
    
    @pytest.mark.asyncio
    async def test_weekday_dancing_query_flow(self):
        """Test the complete flow for the problematic query"""
        query = "any activities for 5 year olds that invovling dancing on weekdays in Burnaby?"
        
        # Phase 1: Temporal Intent Classification
        intent_tool = TemporalIntentClassifierTool()
        mock_context = Mock()
        
        intent_result = await intent_tool.run_async(
            args={"query": query},
            tool_context=mock_context
        )
        
        # Verify temporal intent is correctly classified
        assert intent_result["intent"] == "WEEKDAY_RANGE"
        assert "days_of_week" in intent_result
        assert intent_result["days_of_week"] == ["monday", "tuesday", "wednesday", "thursday", "friday"]
        
        # Phase 2: Keyword Expansion
        keywords = ["dancing"]
        expanded_keywords = expand_keywords(keywords)
        
        # Verify keyword expansion includes relevant terms
        assert "dance" in expanded_keywords
        assert "ballet" in expanded_keywords
        assert "dancing" in expanded_keywords
        assert len(expanded_keywords) > len(keywords)
    
    @pytest.mark.asyncio
    async def test_search_with_expanded_keywords(self):
        """Test that raw_activity_search uses expanded keywords"""
        with patch('multi_tool_agent.tools.activity_search_tools.expand_query_text') as mock_expand:
            with patch('multi_tool_agent.tools.activity_search_tools._perform_balanced_multi_age_search') as mock_search:
                # Set up mocks
                mock_expand.return_value = "dancing dance ballet hip hop"
                mock_search.return_value = []
                mock_context = Mock()
                
                # Call the search function
                result = await raw_activity_search(
                    query="dancing",
                    filters={"age": [5], "location": "Burnaby"},
                    tool_context=mock_context
                )
                
                # Verify expansion was called
                mock_expand.assert_called_once_with("dancing")
                
                # Verify the expanded query was used for search
                mock_search.assert_called_once()
                args = mock_search.call_args[0]
                assert args[0] == "dancing dance ballet hip hop"  # expanded query
    
    def test_no_date_tool_for_weekdays(self):
        """Verify that weekday queries don't trigger date resolution"""
        from multi_tool_agent.utils.time_intent import classify_temporal_intent
        
        weekday_queries = [
            "classes on weekdays",
            "weekday activities",
            "monday to friday programs",
            "during the week"
        ]
        
        for query in weekday_queries:
            intent = classify_temporal_intent(query)
            assert intent == TemporalIntent.WEEKDAY_RANGE
            # This intent should NOT trigger get_current_date
    
    def test_relative_dates_need_resolution(self):
        """Verify that relative dates are properly identified for resolution"""
        from multi_tool_agent.utils.time_intent import classify_temporal_intent
        
        relative_queries = [
            "classes today",
            "tomorrow's activities",
            "next week programs"
        ]
        
        for query in relative_queries:
            intent = classify_temporal_intent(query)
            assert intent == TemporalIntent.RELATIVE_DATE
            # This intent SHOULD trigger get_current_date
    
    def test_combined_temporal_and_activity_keywords(self):
        """Test queries that have both temporal and activity keywords"""
        test_cases = [
            {
                "query": "swimming on weekends",
                "expected_intent": TemporalIntent.WEEKEND,
                "expected_days": ["saturday", "sunday"],
                "activity_keyword": "swimming",
                "expected_expansions": ["swim", "aquatics", "pool"]
            },
            {
                "query": "art classes every monday",
                "expected_intent": TemporalIntent.WEEKDAY_RANGE,
                "expected_days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
                "activity_keyword": "art",
                "expected_expansions": ["arts", "crafts", "painting"]
            }
        ]
        
        from multi_tool_agent.utils.time_intent import classify_temporal_intent
        
        for test_case in test_cases:
            # Test temporal classification
            intent = classify_temporal_intent(test_case["query"])
            assert intent == test_case["expected_intent"]
            
            # Test keyword expansion
            expanded = expand_keywords([test_case["activity_keyword"]])
            for expected in test_case["expected_expansions"]:
                assert expected in expanded
