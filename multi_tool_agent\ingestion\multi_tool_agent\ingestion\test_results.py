#!/usr/bin/env python3
"""Test script to verify the days_of_week_list consistency"""

import json
from collections import Counter

# Load the data
with open('fast_dropin_activities.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print(f"Total activities: {len(data)}")
print("\n=== Testing days_of_week_list consistency ===")

# Check for consistent field presence and format
has_days_list = 0
empty_days_list = 0
populated_days_list = 0
invalid_days_list = 0

day_values = Counter()
sample_activities = []

for activity in data:
    days_list = activity.get('days_of_week_list', [])
    
    if 'days_of_week_list' in activity:
        has_days_list += 1
        
        if isinstance(days_list, list):
            if len(days_list) == 0:
                empty_days_list += 1
            else:
                populated_days_list += 1
                day_values.update(days_list)
        else:
            invalid_days_list += 1
            
        # Collect samples for display
        if len(sample_activities) < 10:
            sample_activities.append({
                'name': activity.get('name', 'Unknown'),
                'days_of_week_list': days_list,
                'booking_type': activity.get('calendar_booking_type'),
                'start_date': activity.get('start_date'),
                'end_date': activity.get('end_date')
            })

print(f"Activities with days_of_week_list field: {has_days_list}")
print(f"Empty days_of_week_list: {empty_days_list}")
print(f"Populated days_of_week_list: {populated_days_list}")
print(f"Invalid days_of_week_list format: {invalid_days_list}")

print("\n=== Day values found ===")
for day, count in day_values.most_common():
    print(f"{day}: {count}")

print("\n=== Sample activities ===")
for activity in sample_activities:
    print(f"- {activity['name'][:50]}... | {activity['days_of_week_list']} | Type: {activity['booking_type']}")

print("\n=== Success rate ===")
success_rate = (populated_days_list / len(data)) * 100
print(f"Activities with populated days_of_week_list: {populated_days_list}/{len(data)} ({success_rate:.1f}%)")
