import pytest
import asyncio
from unittest.mock import Mock
from multi_tool_agent.tools.intent_tools import TemporalIntentClassifierTool

class TestTemporalIntentClassifierTool:
    """Test cases for the temporal intent classifier tool"""
    
    @pytest.fixture
    def tool(self):
        """Create a temporal intent classifier tool instance"""
        return TemporalIntentClassifierTool()
    
    @pytest.fixture
    def mock_context(self):
        """Create a mock tool context"""
        return Mock()
    
    @pytest.mark.asyncio
    async def test_tool_declaration(self, tool):
        """Test that the tool declaration is properly formed"""
        declaration = tool._get_declaration()
        assert declaration.name == "classify_temporal_intent"
        assert "query" in declaration.parameters.properties
        assert declaration.parameters.required == ["query"]
    
    @pytest.mark.asyncio
    async def test_weekday_classification(self, tool, mock_context):
        """Test classification of weekday queries"""
        result = await tool.run_async(
            args={"query": "any activities for 5 year olds that invovling dancing on weekdays in Burnaby?"},
            tool_context=mock_context
        )
        
        assert result["intent"] == "WEEKDAY_RANGE"
        assert "days_of_week" in result
        assert result["days_of_week"] == ["monday", "tuesday", "wednesday", "thursday", "friday"]
    
    @pytest.mark.asyncio
    async def test_weekend_classification(self, tool, mock_context):
        """Test classification of weekend queries"""
        result = await tool.run_async(
            args={"query": "swimming lessons this weekend"},
            tool_context=mock_context
        )
        
        assert result["intent"] == "WEEKEND"
        assert "days_of_week" in result
        assert result["days_of_week"] == ["saturday", "sunday"]
    
    @pytest.mark.asyncio
    async def test_relative_date_classification(self, tool, mock_context):
        """Test classification of relative date queries"""
        result = await tool.run_async(
            args={"query": "dance classes tomorrow"},
            tool_context=mock_context
        )
        
        assert result["intent"] == "RELATIVE_DATE"
        assert "days_of_week" not in result
    
    @pytest.mark.asyncio
    async def test_exact_date_classification(self, tool, mock_context):
        """Test classification of exact date queries"""
        result = await tool.run_async(
            args={"query": "activities on 2025-07-21"},
            tool_context=mock_context
        )
        
        assert result["intent"] == "EXACT_DATE"
        assert "days_of_week" not in result
    
    @pytest.mark.asyncio
    async def test_no_temporal_intent(self, tool, mock_context):
        """Test classification when no temporal intent is found"""
        result = await tool.run_async(
            args={"query": "swimming classes for kids"},
            tool_context=mock_context
        )
        
        assert result["intent"] == "NONE"
        assert "days_of_week" not in result
    
    @pytest.mark.asyncio
    async def test_missing_query(self, tool, mock_context):
        """Test error handling when query is missing"""
        result = await tool.run_async(
            args={},
            tool_context=mock_context
        )
        
        assert "error" in result
        assert result["error"] == "Query is required."
