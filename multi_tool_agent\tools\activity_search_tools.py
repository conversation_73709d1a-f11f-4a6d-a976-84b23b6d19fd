# multi_tool_agent/tools/activity_search_tools.py

import asyncio
import logging
import time
import hashlib
import re
from typing import Dict, List, Optional, Any
from qdrant_client import AsyncQdrantClient, models
from datetime import datetime

from multi_tool_agent.config import AgentConfig
from .schemas import ActivityFilters
from fastembed import TextEmbedding, SparseTextEmbedding

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Simple in-memory cache for frequently accessed queries
_search_cache = {}
_cache_ttl = AgentConfig.CACHE_TTL_SECONDS  # Configurable TTL

# --- NEW: Global cache for Qdrant collection info ---
_qdrant_collection_info_cache: Optional[dict] = None
_qdrant_cache_lock = asyncio.Lock()

async def _get_qdrant_collection_info(client: AsyncQdrantClient) -> dict:
    """Fetches and caches Qdrant collection info to avoid repeated API calls."""
    global _qdrant_collection_info_cache
    async with _qdrant_cache_lock:
        if _qdrant_collection_info_cache is None:
            try:
                logger.info("Fetching Qdrant collection info for index checking...")
                collection_info = await client.get_collection(collection_name=AgentConfig.QDRANT_COLLECTION_NAME)
                # Extract indexed fields from the collection config
                indexed_fields = set()
                
                # Check payload schema for indexed fields
                if hasattr(collection_info, 'config') and hasattr(collection_info.config, 'payload_schema'):
                    indexed_fields.update(collection_info.config.payload_schema.keys())
                
                # Common indexed fields based on our knowledge
                # This is a fallback if the schema isn't accessible
                indexed_fields.update([
                    'city', 'category', 'name', 'is_open', 
                    'min_age_years', 'max_age_years', 'days_of_week_list'
                ])
                
                _qdrant_collection_info_cache = {'indexed_fields': indexed_fields}
                logger.info(f"✅ Cached Qdrant indexed fields: {indexed_fields}")
            except Exception as e:
                logger.error(f"❌ Failed to fetch Qdrant collection info: {e}. Using default field set.")
                # Return a default set of fields we know are typically indexed
                _qdrant_collection_info_cache = {
                    'indexed_fields': {
                        'city', 'category', 'name', 'is_open',
                        'min_age_years', 'max_age_years', 'days_of_week_list'
                    }
                }
        return _qdrant_collection_info_cache

async def _simple_search_fallback(query: str, qdrant_filter: Optional[models.Filter], limit: int = 10) -> List[Dict]:
    """Simple fallback search when embedding models are not available."""
    logger.info(f"🔍 Using simple search fallback for: {query}")

    qdrant_client = AsyncQdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
    try:
        # Simple text-based search using Qdrant's built-in capabilities
        search_response = await qdrant_client.scroll(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            scroll_filter=qdrant_filter,
            limit=limit,
            with_payload=True
        )

        results = [point.payload for point in search_response[0]]
        logger.info(f"✅ Simple search found {len(results)} results")
        return results

    except Exception as e:
        logger.error(f"❌ Simple search fallback failed: {e}")
        return []
    finally:
        await qdrant_client.close()

def _create_qdrant_filter(query: str, filters: Dict[str, Any], indexed_fields: Optional[set] = None) -> Optional[models.Filter]:
    """Creates a Qdrant filter from a structured filter object and query, now with support for negative keywords.
    
    Args:
        query: The search query text
        filters: Dictionary of filter criteria
        indexed_fields: Set of field names that have indexes in Qdrant (for index-aware filtering)
    """
    must_conditions = []
    should_conditions = []
    must_not_conditions = []
    
    # If no indexed fields provided, assume all fields are indexed (backward compatibility)
    if indexed_fields is None:
        indexed_fields = set()

    # --- FIX: Handle negative keywords in the query ---
    if re.search(r'\b(non-swimming|not swimming|no swim)\b', query, re.IGNORECASE):
        logger.info("Negative swimming filter detected in query.")
        must_not_conditions.append(models.FieldCondition(key="category", match=models.MatchValue(value="Aquatics")))
    
    # Check for other negative patterns
    if re.search(r'\b(non-|not |no )\w+', query, re.IGNORECASE):
        logger.info(f"Negative keywords detected in query: '{query}'. Additional filtering may be needed.")

    # Removed the aggressive "Level" filter that was excluding essential scheduling data
    # This allows all gymnastics and swimming level classes to be returned in search results

    # Location filtering using city field (only if indexed)
    if location := filters.get("location"):
        if not indexed_fields or "city" in indexed_fields:
            if "new west" in location.lower() or "new westminster" in location.lower():
                must_conditions.append(models.FieldCondition(key="city", match=models.MatchValue(value="New Westminster")))
            elif "burnaby" in location.lower():
                must_conditions.append(models.FieldCondition(key="city", match=models.MatchValue(value="Burnaby")))
        else:
            logger.warning(f"Skipping location filter - 'city' field not indexed")
    
    # Price Filtering (only if indexed)
    if "max_price" in filters:
        if not indexed_fields or "price_numeric" in indexed_fields:
            try:
                max_price = float(filters["max_price"])
                must_conditions.append(models.FieldCondition(key="price_numeric", range=models.Range(lte=max_price)))
            except (ValueError, TypeError):
                logger.warning(f"Invalid max_price format: {filters['max_price']}")
        else:
            logger.warning(f"Skipping price filter - 'price_numeric' field not indexed")
    
    # Date Filtering - Handle both exact date matching and date ranges (only if indexed)
    if "date" in filters:
        if not indexed_fields or "start_date" in indexed_fields or "end_date" in indexed_fields:
            try:
                # The target date from the agent is in 'YYYY-MM-DD' format
                target_date_str = filters["date"]
                target_date_dt = datetime.strptime(target_date_str, "%Y-%m-%d")
                
                # Convert to the format stored in Qdrant ('DD-Mon-YYYY', e.g., '19-Jul-2025')
                qdrant_date_format = target_date_dt.strftime("%d-%b-%Y")
                
                logger.info(f"📅 Converting date filter: {target_date_str} -> {qdrant_date_format}")
                
                # For DD-Mon-YYYY format, we need to be more flexible since range comparisons 
                # don't work well with this format. Instead, we'll look for activities that:
                # 1. Have the exact target date as start_date, OR
                # 2. Have the exact target date as end_date, OR  
                # 3. Are ongoing courses where start <= target <= end (we'll handle this with broader search)
                
                # Use MatchAny to find activities that either start or end on the target date
                # This will catch single-day activities and ongoing courses that include this date
                date_conditions = [
                    models.FieldCondition(key="start_date", match=models.MatchValue(value=qdrant_date_format)),
                    models.FieldCondition(key="end_date", match=models.MatchValue(value=qdrant_date_format))
                ]
                
                # Add as should condition (OR logic) - activity must match at least one date condition
                should_conditions.extend(date_conditions)
            
            except (ValueError, TypeError) as e:
                # Log the original error for debugging
                logger.error(f"Invalid date format received in filter: {filters['date']}. Error: {e}")
        else:
            logger.warning(f"Skipping date filter - 'start_date' and 'end_date' fields not indexed")

    # Open Status Filtering (only if indexed)
    if filters.get("is_open") is True:
        if not indexed_fields or "is_open" in indexed_fields:
            must_conditions.append(models.FieldCondition(key="is_open", match=models.MatchValue(value=True)))
        else:
            logger.warning(f"Skipping is_open filter - field not indexed")
    
    # Name Contains Filtering - Now enabled with text index on name field (only if indexed)
    if name_filter := filters.get("name_contains"):
        if not indexed_fields or "name" in indexed_fields:
            must_conditions.append(models.FieldCondition(key="name", match=models.MatchText(text=name_filter)))
        else:
            logger.warning(f"Skipping name filter - 'name' field not indexed")

    # --- FIX: Age filtering logic to support scheduling for multiple children --- (only if indexed)
    if ages := filters.get("age"):
        if not indexed_fields or ("min_age_years" in indexed_fields and "max_age_years" in indexed_fields):
            if isinstance(ages, int):
                logger.info(f"Filtering for a single age: {ages}")
                must_conditions.append(models.FieldCondition(key="min_age_years", range=models.Range(lte=ages)))
                must_conditions.append(models.FieldCondition(key="max_age_years", range=models.Range(gte=ages)))
            elif isinstance(ages, list) and len(ages) > 0:
                logger.info(f"Filtering for activities suitable for ANY of ages: {ages}")
                # This creates a filter that matches if an activity is suitable for ANY of the specified ages.
                # This is crucial for finding different activities for different children.
                age_conditions = []
                for age in ages:
                    age_conditions.append(
                        models.Filter(
                            must=[
                                models.FieldCondition(key="min_age_years", range=models.Range(lte=age)),
                                models.FieldCondition(key="max_age_years", range=models.Range(gte=age)),
                            ]
                        )
                    )
                # Add these individual age filters to the main 'should' clause.
                # This means a document must satisfy one of these conditions, along with all 'must' conditions.
                should_conditions.extend(age_conditions)
        else:
            logger.warning(f"Skipping age filter - 'min_age_years' and 'max_age_years' fields not indexed")

    # Robustly handle the day_of_week filter
    if day_filters := filters.get("day_of_week"):
        if isinstance(day_filters, str):
            day_filters_list = [day_filters]
        elif isinstance(day_filters, list):
            day_filters_list = day_filters
        else:
            logger.warning(f"Unsupported type for 'day_of_week' filter: {type(day_filters)}. Skipping.")
            day_filters_list = None

        if day_filters_list:
            if not indexed_fields or "days_of_week_list" in indexed_fields:
                safe_day_filters = [str(day).lower() for day in day_filters_list]
                must_conditions.append(
                    models.FieldCondition(key="days_of_week_list", match=models.MatchAny(any=safe_day_filters))
                )
            else:
                logger.warning(f"Skipping day_of_week filter - 'days_of_week_list' field not indexed")

    # Combine must, should, and must_not conditions into the final filter.
    if must_conditions or should_conditions or must_not_conditions:
        return models.Filter(
            must=must_conditions if must_conditions else None,
            should=should_conditions if should_conditions else None,
            must_not=must_not_conditions if must_not_conditions else None
        )
        
    return None

async def _perform_advanced_search(query: str, qdrant_filter: Optional[models.Filter], limit: int = 10) -> List[Dict]:
    """
    Performs a state-of-the-art 2-stage hybrid search with timeout protection:
    Stage 1: Ultra-fast hybrid search (dense + sparse) with filtering.
    Stage 2: Results are fused using Reciprocal Rank Fusion (RRF).
    """
    start_time = time.time()
    try:
        return await asyncio.wait_for(
            _perform_advanced_search_impl(query, qdrant_filter, limit, start_time),
            timeout=30.0
        )
    except asyncio.TimeoutError:
        logger.error(f"⏰ Search timed out after 30s for query: {query}")
        return await _simple_search_fallback(query, qdrant_filter, limit)
    except Exception as e:
        logger.error(f"❌ Advanced search failed: {e}")
        return await _simple_search_fallback(query, qdrant_filter, limit)

async def _perform_advanced_search_impl(query: str, qdrant_filter: Optional[models.Filter], limit: int, start_time: float) -> List[Dict]:
    """
    Implementation of advanced search using Qdrant's server-side embeddings and hybrid search.
    """
    cache_key = hashlib.md5(f"{query}_{str(qdrant_filter)}_{limit}".encode()).hexdigest()
    if cache_key in _search_cache:
        cached_result, cache_time = _search_cache[cache_key]
        if time.time() - cache_time < _cache_ttl:
            logger.info(f"⚡ Cache hit for query: {query[:50]}... (took {time.time() - start_time:.3f}s)")
            return cached_result

    qdrant_client = AsyncQdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
    try:
        logger.info(f"🔍 Performing HYBRID Qdrant search for: '{query}'")

        # Use the filter as passed in - it should already be index-aware from where it was created
        safe_qdrant_filter = qdrant_filter

        # 1. Initialize embedding models
        dense_embedder = TextEmbedding(model_name=AgentConfig.EMBEDDING_MODEL)
        sparse_embedder = SparseTextEmbedding(model_name=AgentConfig.SPARSE_EMBEDDING_MODEL)
        
        # 2. Embed the query for both dense and sparse models
        dense_query_vector = list(dense_embedder.embed(query))[0]
        sparse_query_vector = list(sparse_embedder.embed(query))[0]
        
        # 3. Perform a hybrid search using Fusion API
        # This will search using both dense and sparse vectors and fuse the results.
        search_response = await qdrant_client.query_points(
            collection_name=AgentConfig.QDRANT_COLLECTION_NAME,
            query=models.FusionQuery(fusion=models.Fusion.RRF),
            prefetch=[
                models.Prefetch(
                    query=dense_query_vector.tolist(),
                    using='dense',
                    limit=limit * 5, # Fetch more results for better fusion
                    filter=safe_qdrant_filter # Apply safe filter
                ),
                models.Prefetch(
                    query=models.SparseVector(
                        indices=sparse_query_vector.indices.tolist(),
                        values=sparse_query_vector.values.tolist(),
                    ),
                    using='sparse',
                    limit=limit * 5, # Fetch more results for better fusion
                    filter=safe_qdrant_filter # Apply safe filter
                )
            ],
            limit=limit,
            with_payload=True
        )

        # The `query_points` method returns a QueryResponse object
        results = [point.payload for point in search_response.points]

        total_time = time.time() - start_time
        logger.info(f"✅ Qdrant hybrid search completed: {len(results)} results in {total_time:.3f}s")
        _search_cache[cache_key] = (results, time.time())
        
        if len(_search_cache) > 100:
            oldest_key = min(_search_cache.keys(), key=lambda k: _search_cache[k][1])
            del _search_cache[oldest_key]

        return results

    except Exception as e:
        logger.error(f"❌ Qdrant hybrid search failed: {e}", exc_info=True)
        logger.info("Falling back to simple scroll due to search error.")
        return await _simple_search_fallback(query, qdrant_filter, limit)
    finally:
        await qdrant_client.close()
def _validate_activity_compatibility(first_activity: Dict, second_activity: Dict) -> bool:
    """
    Validates that activities can be taken together in a day.
    Rules based on proper level progression:
    1. A child can only take ONE level of a specific activity type (can't do Swimming Level 1 and Level 2)
    2. Different activity types are always compatible (Swimming Level 1 + Gymnastics Level 3 is OK)
    3. Non-leveled activities can be combined with anything
    4. Same exact class at same time is not allowed (duplicate booking)
    """
    def extract_activity_info(activity_name: str) -> tuple[str, int]:
        """Extract activity type and level from name like 'Swimming Level 02' -> ('Swimming', 2)"""
        import re
        # Look for 'Level XX' pattern
        level_match = re.search(r'Level\s+(\d+)', activity_name, re.IGNORECASE)
        level = int(level_match.group(1)) if level_match else 0
        
        # Extract activity type (everything before 'Level')
        if level > 0:
            activity_type = re.sub(r'\s+Level\s+\d+.*', '', activity_name, flags=re.IGNORECASE).strip()
        else:
            activity_type = activity_name.strip()
        
        return activity_type, level
    
    first_name = first_activity.get('name', '')
    second_name = second_activity.get('name', '')
    
    # Check if it's the exact same class at the exact same time (duplicate booking)
    if (first_name == second_name and 
        first_activity.get('start_time_iso') == second_activity.get('start_time_iso') and
        first_activity.get('start_date') == second_activity.get('start_date')):
        logger.info(f"❌ Duplicate booking: Same class at same time")
        return False
    
    first_type, first_level = extract_activity_info(first_name)
    second_type, second_level = extract_activity_info(second_name)
    
    # If both are leveled classes of the SAME activity type - NOT ALLOWED
    # A child can only take one level of a specific activity
    if (first_type.lower() == second_type.lower() and 
        first_level > 0 and second_level > 0):
        logger.info(f"❌ Can't take multiple levels of same activity: {first_name} and {second_name}")
        logger.info(f"   → A child must complete {first_type} Level {min(first_level, second_level)} before taking Level {max(first_level, second_level)}")
        return False
    
    # Different activity types are always compatible
    # e.g., Swimming Level 1 + Gymnastics Level 3 is perfectly fine
    if first_type.lower() != second_type.lower():
        if first_level > 0 and second_level > 0:
            logger.info(f"✅ Different activities at different levels: {first_name} + {second_name}")
        return True
    
    # Non-leveled activities are always compatible with everything
    return True

def _find_day_planning_activities(activities: List[Dict]) -> List[Dict]:
    """
    Finds activities that can be combined into a full day schedule.
    More flexible than back-to-back - allows 30+ minute gaps for meals, rest, travel.
    Returns activities grouped by day planning opportunities.
    Now supports cross-facility planning for better variety.
    """
    from datetime import datetime, timedelta

    # Group activities by date only (not facility) to allow cross-facility planning
    date_groups = {}
    for activity in activities:
        start_date = activity.get("start_date")
        if not start_date:
            continue

        if start_date not in date_groups:
            date_groups[start_date] = []
        date_groups[start_date].append(activity)

    back_to_back_results = []

    for date, date_activities in date_groups.items():
        # Sort activities by start time
        timed_activities = []
        for activity in date_activities:
            start_time_str = activity.get("start_time_iso")
            if start_time_str:
                try:
                    # Parse time (format: "HH:MM:SS")
                    start_time = datetime.strptime(start_time_str, "%H:%M:%S").time()
                    timed_activities.append((start_time, activity))
                except ValueError:
                    continue

        # Sort by start time
        timed_activities.sort(key=lambda x: x[0])

        # Find activity pairs with flexible gaps (5 minutes to 3 hours)
        for i in range(len(timed_activities)):
            for j in range(i + 1, len(timed_activities)):
                current_time, current_activity = timed_activities[i]
                next_time, next_activity = timed_activities[j]

                # Calculate time difference
                current_end_str = current_activity.get("end_time_iso")
                if current_end_str:
                    try:
                        current_end = datetime.strptime(current_end_str, "%H:%M:%S").time()
                        next_start = next_time

                        # Convert to datetime for calculation
                        base_date = datetime.today()
                        current_end_dt = datetime.combine(base_date, current_end)
                        next_start_dt = datetime.combine(base_date, next_start)

                        # Check if next class starts within reasonable time (5 min to 3 hours)
                        time_gap = (next_start_dt - current_end_dt).total_seconds() / 60

                        if 5 <= time_gap <= 180:  # 5 minutes to 3 hours gap
                            # Validate activity compatibility
                            if _validate_activity_compatibility(current_activity, next_activity):
                                # Determine gap type for better user experience
                                gap_type = "quick_transition" if time_gap <= 30 else "meal_break" if time_gap <= 90 else "extended_break"
                                
                                # Get facilities info for cross-facility planning
                                first_facility = current_activity.get("facility", "Unknown")
                                second_facility = next_activity.get("facility", "Unknown")
                                facilities = f"{first_facility}" if first_facility == second_facility else f"{first_facility} → {second_facility}"
                                
                                # Create day planning pair
                                day_pair = {
                                    "type": "day_planning_pair",
                                    "facilities": facilities,
                                    "cross_facility": first_facility != second_facility,
                                    "date": current_activity.get("start_date"),
                                    "first_class": current_activity,
                                    "second_class": next_activity,
                                    "time_gap_minutes": int(time_gap),
                                    "gap_type": gap_type,
                                    "total_duration": f"{current_activity.get('start_time_iso', '')} - {next_activity.get('end_time_iso', '')}",
                                    "compatibility_valid": True,
                                    "gap_description": _get_gap_description(time_gap)
                                }
                                back_to_back_results.append(day_pair)
                            else:
                                logger.info(f"⚠️ Skipping incompatible activities: {current_activity.get('name')} → {next_activity.get('name')}")

                    except ValueError:
                        continue

    return back_to_back_results

def _get_gap_description(time_gap_minutes: float) -> str:
    """
    Provides a user-friendly description of the gap between activities.
    """
    if time_gap_minutes <= 15:
        return "Quick transition - perfect for a bathroom break"
    elif time_gap_minutes <= 30:
        return "Short break - time for a snack and some rest"
    elif time_gap_minutes <= 60:
        return "Lunch break - perfect for a meal and relaxation"
    elif time_gap_minutes <= 90:
        return "Extended break - time for lunch and some play"
    elif time_gap_minutes <= 120:
        return "Long break - time for lunch, rest, and activities"
    else:
        return "Very long break - plenty of time for meals, rest, and other activities"

async def _perform_balanced_multi_age_search(
    query: str, 
    filters_dict: Dict[str, Any], 
    final_limit: int
) -> List[Dict]:
    """
    Performs balanced search for multiple ages to avoid search bias.
    When multiple ages are provided, runs separate searches for each age group
    to ensure comprehensive coverage.
    """
    ages = filters_dict.get('age', [])
    
    # If single age or no ages, use the original search
    if not ages or isinstance(ages, int) or len(ages) == 1:
        # Get indexed fields for index-aware filtering
        qdrant_client = AsyncQdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
        try:
            indexed_info = await _get_qdrant_collection_info(qdrant_client)
            indexed_fields = indexed_info.get('indexed_fields', set())
        finally:
            await qdrant_client.close()
        
        qdrant_filter = _create_qdrant_filter(query, filters_dict, indexed_fields)
        return await _perform_advanced_search(query, qdrant_filter, limit=final_limit)
    
    # Multiple ages: run separate searches and combine results
    all_activities = []
    activities_per_age = max(20, final_limit // len(ages))  # Ensure minimum coverage per age
    
    logger.info(f"🎯 Running balanced multi-age search for ages {ages} with {activities_per_age} activities per age")
    
    for age in ages:
        # Create a copy of filters with single age
        single_age_filters = filters_dict.copy()
        single_age_filters['age'] = age
        
        # Create age-specific query for better results
        age_specific_query = _create_age_specific_query(query, age)
        
        logger.info(f"🔍 Searching for age {age} with query: '{age_specific_query}'")
        
        # Get indexed fields for index-aware filtering (reuse connection if possible)
        if 'indexed_fields' not in locals():
            qdrant_client = AsyncQdrantClient(url=AgentConfig.QDRANT_URL, api_key=AgentConfig.QDRANT_API_KEY)
            try:
                indexed_info = await _get_qdrant_collection_info(qdrant_client)
                indexed_fields = indexed_info.get('indexed_fields', set())
            finally:
                await qdrant_client.close()
        
        qdrant_filter = _create_qdrant_filter(age_specific_query, single_age_filters, indexed_fields)
        age_activities = await _perform_advanced_search(age_specific_query, qdrant_filter, limit=activities_per_age)
        
        logger.info(f"📊 Found {len(age_activities)} activities for age {age}")
        all_activities.extend(age_activities)
    
    # Remove duplicates based on record_id
    seen_ids = set()
    unique_activities = []
    for activity in all_activities:
        record_id = activity.get('record_id')
        if record_id and record_id not in seen_ids:
            seen_ids.add(record_id)
            unique_activities.append(activity)
        elif not record_id:  # Include activities without record_id
            unique_activities.append(activity)
    
    logger.info(f"📋 Combined {len(unique_activities)} unique activities from {len(ages)} age-specific searches")
    
    # Sort by relevance score if available, otherwise by price
    def sort_key(activity):
        return (activity.get('score', 0), -activity.get('price_numeric', 0))
    
    unique_activities.sort(key=sort_key, reverse=True)
    
    return unique_activities[:final_limit]

def _create_age_specific_query(base_query: str, age: int) -> str:
    """
    Creates age-appropriate query modifications to improve search relevance.
    """
    # For very young children (2-4), emphasize beginner/preschool terms
    if age <= 4:
        age_terms = ["beginner", "preschool", "caregiver", "parent", "toddler", "intro"]
        return f"{base_query} {' '.join(age_terms)}"
    
    # For elementary age (5-8), include level-based terms
    elif age <= 8:
        age_terms = ["children", "level 1", "level 2", "elementary", "beginner", "intermediate"]
        return f"{base_query} {' '.join(age_terms)}"
    
    # For older children (9+), include advanced terms
    else:
        age_terms = ["advanced", "level 3", "level 4", "intermediate", "youth"]
        return f"{base_query} {' '.join(age_terms)}"

async def raw_activity_search(query: str, filters: Dict[str, Any]) -> Dict[str, Any]:
    """Performs an advanced, balanced search for kids' activities and returns the raw data."""
    logger.info(f"🧠 Running RAW search. Query: '{query}', Filters: {filters}")
    try:
        validated_filters = ActivityFilters.model_validate(filters or {})
        filters_dict = validated_filters.model_dump(exclude_none=True)
        
        activities = await _perform_balanced_multi_age_search(query, filters_dict, 150)
        return {"status": "success", "results": activities}
    except Exception as e:
        logger.error(f"Error in raw_activity_search: {e}", exc_info=True)
        return {"status": "error", "message": f"Search failed: {str(e)[:200]}."}


# --- NEW WEB SEARCH TOOL ---
# Note: This is defined here for conceptual clarity, but in the final implementation,
# the Planner agent will use the google_search tool directly.
async def web_activity_search(query: str) -> str:
    """
    Performs a web search to find information about local children's activities
    when the internal database has no results. Use this as a fallback.

    Args:
        query: The search query, e.g., "kids gymnastics classes New Westminster".
    
    Returns:
        A summary of the web search results.
    """
    logger.info(f"🌐 Performing web search for: '{query}'")
    # This is a placeholder for how you would call the google_search tool.
    # In the real implementation, the Planner agent will have google_search directly.
    # We define this function for conceptual clarity in the Planner's toolset.
    # In the final implementation, the Planner will just have the `google_search` object.
    return "Web search is a fallback option. The Planner agent will use the built-in google_search tool directly."
