"""
Test script to verify abundant capacity (200 concurrent + 10 RPM = ~4000 RPM effective) 
enables rich, multi-tool dialogue experiences.
"""

import asyncio
import time
import logging
from google.adk.session import InMemorySessionService
from google.adk.memory import GraphitiApiMemoryService
from google.adk.runner import Runner
from google.genai import types
from multi_tool_agent.orchestrator import root_agent
from multi_tool_agent.config import AgentConfig

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_rich_dialogue_experience():
    """Test rich, multi-tool dialogue with abundant capacity."""
    
    print("🚀 Testing Abundant Capacity Rich Dialogue")
    print(f"📊 Config: ENABLE_API_OPTIMIZATION={AgentConfig.ENABLE_API_OPTIMIZATION}")
    print(f"📊 Config: MAX_API_CALLS_PER_REQUEST={AgentConfig.MAX_API_CALLS_PER_REQUEST}")
    print(f"📊 Config: ULTRA_LOW_API_MODE={AgentConfig.ULTRA_LOW_API_MODE}")
    print(f"📊 DeepInfra: 200 concurrent requests (~4000 RPM effective)")
    print(f"🤖 Agent: {root_agent.name}")
    print("=" * 70)
    
    # --- Session Management ---
    session_service = InMemorySessionService()
    APP_NAME, USER_ID, SESSION_ID = "rich_dialogue_app", "test_user_rich", "rich_session_01"
    
    await session_service.create_session(
        app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID, state={}
    )
    
    # --- Memory Service (optional) ---
    try:
        memory_service = GraphitiApiMemoryService(base_url="http://localhost:8001")
        print("✅ Memory service connected")
    except Exception as e:
        print(f"⚠️ Memory service not available: {e}")
        memory_service = None
    
    # --- Runner ---
    runner = Runner(
        agent=root_agent,
        app_name=APP_NAME,
        session_service=session_service,
        memory_service=memory_service
    )
    
    # --- Rich Dialogue Test Cases ---
    rich_dialogue_turns = [
        "Hi! I'm looking for activities for my kids",
        "I have a 5-year-old and an 8-year-old",
        "We're in Burnaby and interested in swimming",
        "What are the best options for weekday mornings?",
        "Can you find back-to-back classes so I can drop both kids off together?",
        "What about costs and registration details?",
        "Are there any beginner-friendly options?",
        "What other activities are available nearby?",
        "Can you create a weekly schedule with multiple activities?"
    ]
    
    start_time = time.time()
    response_times = []
    tool_usage_count = 0
    
    for i, user_input in enumerate(rich_dialogue_turns, 1):
        turn_start = time.time()
        
        print(f"\n🗣️ Turn {i}/{len(rich_dialogue_turns)}: {user_input}")
        print("-" * 50)
        
        content = types.Content(role='user', parts=[types.Part(text=user_input)])
        
        final_response = "No response received"
        turn_tool_calls = 0
        
        async for event in runner.run_async(
            user_id=USER_ID,
            session_id=SESSION_ID,
            new_message=content
        ):
            # Count tool calls
            if hasattr(event, 'actions') and event.actions and hasattr(event.actions, 'function_calls'):
                turn_tool_calls += len(event.actions.function_calls or [])
            
            if event.is_final_response():
                if event.content and event.content.parts and event.content.parts[0].text:
                    final_response = event.content.parts[0].text.strip()
                break
        
        turn_time = time.time() - turn_start
        response_times.append(turn_time)
        tool_usage_count += turn_tool_calls
        
        print(f"🤖 Agent ({turn_time:.2f}s, {turn_tool_calls} tools): {final_response[:200]}...")
        
        # Minimal delay - we have abundant capacity
        await asyncio.sleep(0.2)
    
    total_time = time.time() - start_time
    avg_response_time = sum(response_times) / len(response_times)
    avg_tools_per_turn = tool_usage_count / len(rich_dialogue_turns)
    
    print("\n" + "=" * 70)
    print("📊 RICH DIALOGUE PERFORMANCE METRICS:")
    print(f"⏱️ Total conversation time: {total_time:.2f}s")
    print(f"⏱️ Average response time: {avg_response_time:.2f}s")
    print(f"⏱️ Turns per minute: {len(rich_dialogue_turns) / (total_time / 60):.1f}")
    print(f"🛠️ Total tool calls: {tool_usage_count}")
    print(f"🛠️ Average tools per turn: {avg_tools_per_turn:.1f}")
    print(f"🎯 With ~4000 RPM: Should handle rich, multi-tool responses easily")
    
    if avg_response_time < 3.0:
        print("✅ EXCELLENT: Sub-3s responses with rich tool usage")
    elif avg_response_time < 5.0:
        print("✅ VERY GOOD: Sub-5s responses - great UX")
    elif avg_response_time < 10.0:
        print("✅ GOOD: Sub-10s responses - acceptable")
    else:
        print("⚠️ SLOW: Over 10s - may need optimization")

async def test_concurrent_rich_conversations():
    """Test multiple concurrent rich conversations to stress-test capacity."""
    
    print("\n🔄 Testing Concurrent Rich Conversations")
    print("=" * 70)
    
    async def rich_conversation(conv_id: int):
        """Run a rich conversation with multiple tool calls."""
        session_service = InMemorySessionService()
        session_id = f"rich_concurrent_session_{conv_id}"
        
        await session_service.create_session(
            app_name="rich_concurrent_test", user_id=f"user_{conv_id}", session_id=session_id, state={}
        )
        
        runner = Runner(
            agent=root_agent,
            app_name="rich_concurrent_test",
            session_service=session_service,
            memory_service=None
        )
        
        # Rich query that should trigger multiple tools
        query = f"I need a comprehensive activity plan for my {4 + conv_id}-year-old in Burnaby. Please find swimming classes, check schedules, and suggest back-to-back options with full details including costs and registration."
        content = types.Content(role='user', parts=[types.Part(text=query)])
        
        start_time = time.time()
        tool_calls = 0
        
        async for event in runner.run_async(
            user_id=f"user_{conv_id}",
            session_id=session_id,
            new_message=content
        ):
            # Count tool usage
            if hasattr(event, 'actions') and event.actions and hasattr(event.actions, 'function_calls'):
                tool_calls += len(event.actions.function_calls or [])
                
            if event.is_final_response():
                response_time = time.time() - start_time
                print(f"✅ Rich conversation {conv_id} completed in {response_time:.2f}s with {tool_calls} tool calls")
                return response_time, tool_calls
        
        return 0, 0
    
    # Run 5 concurrent rich conversations
    start_time = time.time()
    tasks = [rich_conversation(i) for i in range(1, 6)]
    results = await asyncio.gather(*tasks)
    total_time = time.time() - start_time
    
    response_times = [r[0] for r in results]
    total_tool_calls = sum(r[1] for r in results)
    
    print(f"\n📊 Concurrent rich conversation results:")
    print(f"⏱️ Total time for 5 rich conversations: {total_time:.2f}s")
    print(f"⏱️ Individual response times: {[f'{t:.2f}s' for t in response_times]}")
    print(f"🛠️ Total tool calls across all conversations: {total_tool_calls}")
    print(f"🛠️ Average tool calls per conversation: {total_tool_calls / 5:.1f}")
    print(f"🎯 With 200 concurrent requests: Should handle 5+ rich conversations easily")

async def test_rapid_fire_requests():
    """Test rapid-fire requests to verify rate limit handling."""
    
    print("\n⚡ Testing Rapid-Fire Request Handling")
    print("=" * 70)
    
    session_service = InMemorySessionService()
    await session_service.create_session(
        app_name="rapid_fire_test", user_id="rapid_user", session_id="rapid_session", state={}
    )
    
    runner = Runner(
        agent=root_agent,
        app_name="rapid_fire_test",
        session_service=session_service,
        memory_service=None
    )
    
    # Send 10 requests as fast as possible
    queries = [
        f"Find swimming for {i+3} year old in Burnaby"
        for i in range(10)
    ]
    
    start_time = time.time()
    
    async def send_request(query_id, query):
        content = types.Content(role='user', parts=[types.Part(text=query)])
        request_start = time.time()
        
        async for event in runner.run_async(
            user_id="rapid_user",
            session_id=f"rapid_session_{query_id}",
            new_message=content
        ):
            if event.is_final_response():
                response_time = time.time() - request_start
                print(f"⚡ Request {query_id} completed in {response_time:.2f}s")
                return response_time
        return 0
    
    # Send all requests simultaneously
    tasks = [send_request(i, query) for i, query in enumerate(queries)]
    response_times = await asyncio.gather(*tasks)
    total_time = time.time() - start_time
    
    print(f"\n📊 Rapid-fire test results:")
    print(f"⏱️ Total time for 10 simultaneous requests: {total_time:.2f}s")
    print(f"⏱️ Average response time: {sum(response_times) / len(response_times):.2f}s")
    print(f"⏱️ Requests per second achieved: {len(queries) / total_time:.1f}")
    print(f"🎯 With 200 concurrent limit: Should handle bursts easily")

if __name__ == "__main__":
    print("🚀 Testing Abundant Capacity (200 concurrent + 10 RPM = ~4000 RPM)")
    print(f"📋 Agent: {root_agent.name}")
    print(f"📋 Description: {root_agent.description}")
    
    asyncio.run(test_rich_dialogue_experience())
    asyncio.run(test_concurrent_rich_conversations())
    asyncio.run(test_rapid_fire_requests())
