# api/setup_db.py
import asyncio
import logging
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from api.main import get_graphiti_client # Assuming get_graphiti_client is in main

logging.basicConfig(level=logging.INFO)

async def setup():
    """Connects to Graphiti and builds all necessary indexes and constraints."""
    logging.info("Connecting to Graphiti and preparing database schema...")
    graphiti_client = get_graphiti_client()
    try:
        # Try to build indices and constraints, but don't delete existing ones
        try:
            await graphiti_client.build_indices_and_constraints(delete_existing=False)
            logging.info("✅ Graphiti indices and constraints created.")
        except Exception as e:
            logging.warning(f"Some indices/constraints already exist: {e}")
            # Try without deleting existing ones
            try:
                await graphiti_client.build_indices_and_constraints(delete_existing=False)
            except Exception as e2:
                logging.warning(f"Could not create some indices/constraints: {e2}")
        
        # Manually add the crucial uniqueness constraint for our HITL workflow.
        # This ensures that "Robotics Club" can only be proposed once.
        try:
            async with graphiti_client.driver.session() as session:
                await session.run(
                    "CREATE CONSTRAINT unique_concept_name IF NOT EXISTS FOR (p:ProposedConcept) REQUIRE p.name IS UNIQUE"
                )
            logging.info("✅ ProposedConcept constraint added.")
        except Exception as e:
            logging.warning(f"ProposedConcept constraint may already exist: {e}")
        
        logging.info("✅ Database setup complete. Indexes and constraints are ready.")
    except Exception as e:
        logging.error(f"❌ Database setup failed: {e}", exc_info=True)
    finally:
        await graphiti_client.close()

if __name__ == "__main__":
    asyncio.run(setup())

