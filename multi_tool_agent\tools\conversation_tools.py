"""
Conversation Context Tools for Multi-Turn Dialogue
Manages dialogue state and context across conversation turns.
"""

import logging
from typing import Dict, Any, List

logger = logging.getLogger(__name__)

# Global conversation context storage
# In production, this would be backed by <PERSON><PERSON> or a database
_conversation_contexts: Dict[str, Dict[str, Any]] = {}

def get_conversation_context(session_id: str = "default") -> Dict[str, Any]:
    """
    Retrieve stored conversation context for a session.

    Args:
        session_id: Session identifier

    Returns:
        Dictionary of stored context information
    """
    context = _conversation_contexts.get(session_id, {})
    logger.info(f"📖 Retrieved conversation context for {session_id}: {context}")
    return context

def store_conversation_context(session_id: str = "default", **context_data) -> str:
    """
    Store conversation context information.

    Args:
        session_id: Session identifier
        **context_data: Key-value pairs to store

    Returns:
        Success message
    """
    if session_id not in _conversation_contexts:
        _conversation_contexts[session_id] = {}

    _conversation_contexts[session_id].update(context_data)
    logger.info(f"📝 Stored conversation context for {session_id}: {context_data}")
    return f"Stored context: {list(context_data.keys())}"

def check_sufficient_info(session_id: str = "default") -> Dict[str, Any]:
    """
    Check if we have sufficient information to search for activities.

    Args:
        session_id: Session identifier

    Returns:
        Dictionary with sufficiency check results
    """
    context = get_conversation_context(session_id)

    # Essential information required
    has_age = bool(context.get('child_age') or context.get('age'))
    has_location = bool(context.get('location') or context.get('city') or context.get('area'))

    # At least one activity indicator
    has_activity_info = bool(
        context.get('activity_type') or
        context.get('activity_keywords') or
        context.get('specific_request') or
        context.get('activity')
    )

    sufficient = has_age and has_location and has_activity_info

    # Determine what's missing
    missing = []
    if not has_age:
        missing.append("child's age")
    if not has_location:
        missing.append("location/city")
    if not has_activity_info:
        missing.append("activity type")

    result = {
        "sufficient": sufficient,
        "has_age": has_age,
        "has_location": has_location,
        "has_activity_info": has_activity_info,
        "missing": missing,
        "context": context
    }

    logger.info(f"✅ Sufficiency check for {session_id}: {result}")
    return result

def get_next_question(session_id: str = "default") -> str:
    """
    Get the next clarifying question to ask the user.

    Args:
        session_id: Session identifier

    Returns:
        Next question to ask, or empty string if no questions needed
    """
    info_check = check_sufficient_info(session_id)

    if info_check["sufficient"]:
        return ""

    missing = info_check["missing"]

    # Prioritize the most important missing information
    if "child's age" in missing:
        return "How old is your child?"
    elif "location/city" in missing:
        return "What city or area are you looking in? I have activities in New Westminster and Burnaby."
    elif "activity type" in missing:
        return "What type of activities are you interested in? (e.g., swimming, art, sports, music)"

    return "Could you provide more details about what you're looking for?"

# Legacy class for backward compatibility
class ConversationContextTool:
    """
    Manages dialogue state across turns for conversational agents.
    Stores and retrieves conversation context to enable natural multi-turn dialogue.
    """
    
    def __init__(self):
        super().__init__(
            name="conversation_context",
            description="Manages conversation context and dialogue state across multiple turns"
        )
        # In-memory storage for conversation context
        # In production, this would be backed by a persistent store
        self._conversation_contexts: Dict[str, Dict[str, Any]] = {}
    
    def _get_session_key(self, ctx: ToolContext) -> str:
        """Generate a unique key for the current session."""
        # Try to get session ID from context
        if hasattr(ctx, 'session') and ctx.session and hasattr(ctx.session, 'id'):
            return f"session_{ctx.session.id}"
        
        # Fallback to user ID if available
        if hasattr(ctx, 'user_id') and ctx.user_id:
            return f"user_{ctx.user_id}"
        
        # Default fallback
        return "default_session"
    
    def store_context(self, ctx: ToolContext, key_info: Dict[str, Any]) -> str:
        """
        Store critical information discovered during conversation.
        
        Args:
            ctx: Tool context
            key_info: Dictionary of key information to store
            
        Returns:
            Success message
        """
        session_key = self._get_session_key(ctx)
        
        if session_key not in self._conversation_contexts:
            self._conversation_contexts[session_key] = {}
        
        # Update context with new information
        self._conversation_contexts[session_key].update(key_info)
        
        logger.info(f"📝 Stored conversation context for {session_key}: {key_info}")
        return f"Stored context: {list(key_info.keys())}"
    
    def get_context(self, ctx: ToolContext) -> Dict[str, Any]:
        """
        Retrieve stored conversation context.
        
        Args:
            ctx: Tool context
            
        Returns:
            Dictionary of stored context information
        """
        session_key = self._get_session_key(ctx)
        context = self._conversation_contexts.get(session_key, {})
        
        logger.info(f"📖 Retrieved conversation context for {session_key}: {context}")
        return context
    
    def get_pending_questions(self, ctx: ToolContext) -> List[str]:
        """
        Return what information still needs clarification.
        
        Args:
            ctx: Tool context
            
        Returns:
            List of questions that still need answers
        """
        context = self.get_context(ctx)
        pending = []
        
        # Check for essential information
        if not context.get('child_age'):
            pending.append("What is your child's age?")
        
        if not context.get('location'):
            pending.append("What area/city are you looking in?")
        
        if not context.get('activity_type') and not context.get('activity_keywords'):
            pending.append("What type of activities are you interested in?")
        
        # Check for important but not critical information
        if not context.get('schedule_preference'):
            pending.append("Do you have any day/time preferences?")
        
        logger.info(f"❓ Pending questions for session: {pending}")
        return pending
    
    def clear_context(self, ctx: ToolContext) -> str:
        """
        Clear conversation context for the current session.
        
        Args:
            ctx: Tool context
            
        Returns:
            Success message
        """
        session_key = self._get_session_key(ctx)
        
        if session_key in self._conversation_contexts:
            del self._conversation_contexts[session_key]
            logger.info(f"🧹 Cleared conversation context for {session_key}")
            return f"Cleared context for {session_key}"
        
        return "No context to clear"
    
    def has_sufficient_info(self, ctx: ToolContext) -> bool:
        """
        Check if we have enough information to proceed with activity search.
        
        Args:
            ctx: Tool context
            
        Returns:
            True if we have sufficient information for search
        """
        context = self.get_context(ctx)
        
        # Essential information required
        has_age = bool(context.get('child_age'))
        has_location = bool(context.get('location'))
        
        # At least one activity indicator
        has_activity_info = bool(
            context.get('activity_type') or 
            context.get('activity_keywords') or 
            context.get('specific_request')
        )
        
        sufficient = has_age and has_location and has_activity_info
        
        logger.info(f"✅ Sufficient info check: age={has_age}, location={has_location}, activity={has_activity_info} -> {sufficient}")
        return sufficient

    async def run_async(self, ctx: ToolContext) -> str:
        """
        Main tool execution method.
        This is a utility tool that doesn't perform direct actions.
        """
        return "ConversationContextTool is ready. Use specific methods like store_context(), get_context(), etc."
