"""
Conversation Context Tools for Multi-Turn Dialogue
Manages dialogue state and context across conversation turns.
"""

import logging
from typing import Dict, Any, List

logger = logging.getLogger(__name__)

# Global conversation context storage
# In production, this would be backed by <PERSON><PERSON> or a database
_conversation_contexts: Dict[str, Dict[str, Any]] = {}

def get_conversation_context(session_id: str = "default") -> Dict[str, Any]:
    """
    Retrieve stored conversation context for a session.

    Args:
        session_id: Session identifier

    Returns:
        Dictionary of stored context information
    """
    context = _conversation_contexts.get(session_id, {})
    logger.info(f"📖 Retrieved conversation context for {session_id}: {context}")
    return context

def store_conversation_context(session_id: str = "default", **context_data) -> str:
    """
    Store conversation context information.

    Args:
        session_id: Session identifier
        **context_data: Key-value pairs to store

    Returns:
        Success message
    """
    if session_id not in _conversation_contexts:
        _conversation_contexts[session_id] = {}

    _conversation_contexts[session_id].update(context_data)
    logger.info(f"📝 Stored conversation context for {session_id}: {context_data}")
    return f"Stored context: {list(context_data.keys())}"

def check_sufficient_info(session_id: str = "default") -> Dict[str, Any]:
    """
    Check if we have sufficient information to search for activities.

    Args:
        session_id: Session identifier

    Returns:
        Dictionary with sufficiency check results
    """
    context = get_conversation_context(session_id)

    # Essential information required
    has_age = bool(context.get('child_age') or context.get('age'))
    has_location = bool(context.get('location') or context.get('city') or context.get('area'))

    # At least one activity indicator
    has_activity_info = bool(
        context.get('activity_type') or
        context.get('activity_keywords') or
        context.get('specific_request') or
        context.get('activity')
    )

    sufficient = has_age and has_location and has_activity_info

    # Determine what's missing
    missing = []
    if not has_age:
        missing.append("child's age")
    if not has_location:
        missing.append("location/city")
    if not has_activity_info:
        missing.append("activity type")

    result = {
        "sufficient": sufficient,
        "has_age": has_age,
        "has_location": has_location,
        "has_activity_info": has_activity_info,
        "missing": missing,
        "context": context
    }

    logger.info(f"✅ Sufficiency check for {session_id}: {result}")
    return result

def get_next_question(session_id: str = "default") -> str:
    """
    Get the next clarifying question to ask the user.

    Args:
        session_id: Session identifier

    Returns:
        Next question to ask, or empty string if no questions needed
    """
    info_check = check_sufficient_info(session_id)

    if info_check["sufficient"]:
        return ""

    missing = info_check["missing"]

    # Prioritize the most important missing information
    if "child's age" in missing:
        return "How old is your child?"
    elif "location/city" in missing:
        return "What city or area are you looking in? I have activities in New Westminster and Burnaby."
    elif "activity type" in missing:
        return "What type of activities are you interested in? (e.g., swimming, art, sports, music)"

    return "Could you provide more details about what you're looking for?"

# Legacy class for backward compatibility
class ConversationContextTool:
    """
    Legacy wrapper for conversation context functions.
    Use the standalone functions above for new code.
    """

    def __init__(self):
        self.name = "conversation_context"
        self.description = "Manages conversation context and dialogue state across multiple turns"
