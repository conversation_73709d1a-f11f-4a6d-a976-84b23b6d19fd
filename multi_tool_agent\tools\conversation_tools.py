"""
Conversation Context Tools for Multi-Turn Dialogue
Manages dialogue state and context across conversation turns.
"""

import logging
from typing import Dict, Any, List, Optional
from google.adk.tools import BaseTool, ToolContext

logger = logging.getLogger(__name__)

class ConversationContextTool(BaseTool):
    """
    Manages dialogue state across turns for conversational agents.
    Stores and retrieves conversation context to enable natural multi-turn dialogue.
    """
    
    def __init__(self):
        super().__init__(
            name="conversation_context",
            description="Manages conversation context and dialogue state across multiple turns"
        )
        # In-memory storage for conversation context
        # In production, this would be backed by a persistent store
        self._conversation_contexts: Dict[str, Dict[str, Any]] = {}
    
    def _get_session_key(self, ctx: ToolContext) -> str:
        """Generate a unique key for the current session."""
        # Try to get session ID from context
        if hasattr(ctx, 'session') and ctx.session and hasattr(ctx.session, 'id'):
            return f"session_{ctx.session.id}"
        
        # Fallback to user ID if available
        if hasattr(ctx, 'user_id') and ctx.user_id:
            return f"user_{ctx.user_id}"
        
        # Default fallback
        return "default_session"
    
    def store_context(self, ctx: ToolContext, key_info: Dict[str, Any]) -> str:
        """
        Store critical information discovered during conversation.
        
        Args:
            ctx: Tool context
            key_info: Dictionary of key information to store
            
        Returns:
            Success message
        """
        session_key = self._get_session_key(ctx)
        
        if session_key not in self._conversation_contexts:
            self._conversation_contexts[session_key] = {}
        
        # Update context with new information
        self._conversation_contexts[session_key].update(key_info)
        
        logger.info(f"📝 Stored conversation context for {session_key}: {key_info}")
        return f"Stored context: {list(key_info.keys())}"
    
    def get_context(self, ctx: ToolContext) -> Dict[str, Any]:
        """
        Retrieve stored conversation context.
        
        Args:
            ctx: Tool context
            
        Returns:
            Dictionary of stored context information
        """
        session_key = self._get_session_key(ctx)
        context = self._conversation_contexts.get(session_key, {})
        
        logger.info(f"📖 Retrieved conversation context for {session_key}: {context}")
        return context
    
    def get_pending_questions(self, ctx: ToolContext) -> List[str]:
        """
        Return what information still needs clarification.
        
        Args:
            ctx: Tool context
            
        Returns:
            List of questions that still need answers
        """
        context = self.get_context(ctx)
        pending = []
        
        # Check for essential information
        if not context.get('child_age'):
            pending.append("What is your child's age?")
        
        if not context.get('location'):
            pending.append("What area/city are you looking in?")
        
        if not context.get('activity_type') and not context.get('activity_keywords'):
            pending.append("What type of activities are you interested in?")
        
        # Check for important but not critical information
        if not context.get('schedule_preference'):
            pending.append("Do you have any day/time preferences?")
        
        logger.info(f"❓ Pending questions for session: {pending}")
        return pending
    
    def clear_context(self, ctx: ToolContext) -> str:
        """
        Clear conversation context for the current session.
        
        Args:
            ctx: Tool context
            
        Returns:
            Success message
        """
        session_key = self._get_session_key(ctx)
        
        if session_key in self._conversation_contexts:
            del self._conversation_contexts[session_key]
            logger.info(f"🧹 Cleared conversation context for {session_key}")
            return f"Cleared context for {session_key}"
        
        return "No context to clear"
    
    def has_sufficient_info(self, ctx: ToolContext) -> bool:
        """
        Check if we have enough information to proceed with activity search.
        
        Args:
            ctx: Tool context
            
        Returns:
            True if we have sufficient information for search
        """
        context = self.get_context(ctx)
        
        # Essential information required
        has_age = bool(context.get('child_age'))
        has_location = bool(context.get('location'))
        
        # At least one activity indicator
        has_activity_info = bool(
            context.get('activity_type') or 
            context.get('activity_keywords') or 
            context.get('specific_request')
        )
        
        sufficient = has_age and has_location and has_activity_info
        
        logger.info(f"✅ Sufficient info check: age={has_age}, location={has_location}, activity={has_activity_info} -> {sufficient}")
        return sufficient

    async def run_async(self, ctx: ToolContext) -> str:
        """
        Main tool execution method.
        This is a utility tool that doesn't perform direct actions.
        """
        return "ConversationContextTool is ready. Use specific methods like store_context(), get_context(), etc."
