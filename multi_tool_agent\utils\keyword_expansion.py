# multi_tool_agent/utils/keyword_expansion.py
import json
import os
import logging
from typing import List, Dict, Set

logger = logging.getLogger(__name__)

_synonym_map: Dict[str, List[str]] = {}
_synonym_file_path = os.path.join(os.path.dirname(__file__), '..', 'resources', 'activity_synonyms.json')

def _load_synonyms():
    """Loads the synonym map from the JSON file."""
    global _synonym_map
    if not _synonym_map:
        try:
            with open(_synonym_file_path, 'r') as f:
                _synonym_map = json.load(f)
            logger.info(f"✅ Synonym map loaded successfully with {len(_synonym_map)} entries.")
        except FileNotFoundError:
            logger.error(f"❌ Synonym file not found at: {_synonym_file_path}")
            _synonym_map = {}
        except json.JSONDecodeError:
            logger.error(f"❌ Error decoding JSON from synonym file: {_synonym_file_path}")
            _synonym_map = {}

def expand_keywords(keywords: List[str]) -> List[str]:
    """
    Expands a list of keywords using the synonym map.
    Includes original keywords and their synonyms, removing duplicates.
    
    Args:
        keywords: List of keywords to expand
        
    Returns:
        Sorted list of expanded keywords (including originals)
    """
    if not _synonym_map:
        _load_synonyms()
        
    expanded_set: Set[str] = set()
    
    for keyword in keywords:
        # Clean and normalize the keyword
        key_lower = keyword.lower().strip()
        
        # Add the original keyword
        expanded_set.add(key_lower)
        
        # Add direct synonyms if they exist
        if key_lower in _synonym_map:
            expanded_set.update(_synonym_map[key_lower])
            logger.debug(f"Expanded '{key_lower}' to include: {_synonym_map[key_lower]}")
        
        # Handle basic stemming for common cases
        # e.g., "swimming" -> "swim", "dancer" -> "dance"
        stem_variants = []
        if key_lower.endswith('ing'):
            stem_variants.append(key_lower[:-3])  # swimming -> swimm
            stem_variants.append(key_lower[:-4])  # dancing -> danc
        elif key_lower.endswith('er'):
            stem_variants.append(key_lower[:-2])  # dancer -> danc
        
        for variant in stem_variants:
            if variant in _synonym_map:
                expanded_set.update(_synonym_map[variant])
                logger.debug(f"Stem variant '{variant}' added synonyms: {_synonym_map[variant]}")
    
    result = sorted(list(expanded_set))
    logger.info(f"📝 Keywords {keywords} expanded to {len(result)} terms: {result[:5]}...")
    return result

def expand_query_text(query: str) -> str:
    """
    Expands a natural language query by identifying and expanding key activity terms.
    
    Args:
        query: Natural language query string
        
    Returns:
        Expanded query string
    """
    if not _synonym_map:
        _load_synonyms()
    
    # Simple tokenization - in production, consider using nltk or spacy
    words = query.lower().split()
    expanded_words = []
    
    for word in words:
        if word in _synonym_map:
            # Add the top 3 most relevant synonyms
            synonyms = _synonym_map[word][:3]
            expanded_words.extend([word] + synonyms)
        else:
            expanded_words.append(word)
    
    # Remove duplicates while preserving order
    seen = set()
    result = []
    for word in expanded_words:
        if word not in seen:
            seen.add(word)
            result.append(word)
    
    return ' '.join(result)
