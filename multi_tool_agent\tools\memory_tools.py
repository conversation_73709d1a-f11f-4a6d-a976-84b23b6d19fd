# multi_tool_agent/tools/memory_tools.py
import httpx
import logging
from google.adk.tools import BaseTool, ToolContext
# **CORRECTED IMPORT** for schema objects
from google.genai.types import FunctionDeclaration, Schema, Type

# Access services via tool_context at runtime to avoid circular imports

logger = logging.getLogger(__name__)

class RetrieveInfoFromMemory(BaseTool):
    """
    A tool to retrieve relevant facts and past conversations from long-term memory.
    """
    def __init__(self):
        super().__init__(
            name="retrieve_info_from_memory",
            description=(
                "Retrieves relevant facts and past conversations from your long-term memory "
                "to answer a user's question or recall context. Use this to remember user "
                "preferences, children's details, or past activities."
            )
        )

    def _get_declaration(self) -> FunctionDeclaration:
        """Defines the tool's schema for the LLM. Notice 'tool_context' is not here."""
        return FunctionDeclaration(
            name=self.name,
            description=self.description,
            parameters=Schema(
                type=Type.OBJECT,
                properties={
                    "query": Schema(
                        type=Type.STRING,
                        description="A natural language query describing the information you want to recall from memory.",
                    )
                },
                required=["query"],
            ),
        )

    async def run_async(self, *, args: dict, tool_context: ToolContext) -> dict:
        """The logic that executes when the tool is called by the ADK framework."""
        query = args.get("query")
        if not query:
            return {"status": "error", "message": "A query is required to retrieve information from memory."}

        try:
            invocation_ctx = tool_context._invocation_context
            memory_service = invocation_ctx.memory_service
            
            if not memory_service:
                 return {"status": "error", "message": "Memory service is not available."}

            user_id = invocation_ctx.user_id
            app_name = invocation_ctx.app_name
            
            if hasattr(memory_service, 'set_user_id'):
                memory_service.set_user_id(user_id)
            
            response = await memory_service.search_memory(app_name=app_name, user_id=user_id, query=query)

            if not response.memories:
                return {"status": "success", "found_anything": False, "facts": []}

            facts = [memory.content.parts[0].text for memory in response.memories if memory.content.parts]
            return {"status": "success", "found_anything": True, "facts": facts}

        except Exception as e:
            logger.error(f"ERROR in retrieve_info_from_memory: {e}", exc_info=True)
            return {"status": "error", "message": f"An error occurred while accessing my memory: {e}"}

class ProposeNewConcept(BaseTool):
    """
    A tool to log new, unrecognized concepts for human review.
    """
    def __init__(self):
        super().__init__(
            name="propose_new_concept",
            description=(
                "Use this tool when a user mentions an important activity type or category "
                "that you do not recognize. This logs the concept for review by a human."
            )
        )

    def _get_declaration(self) -> FunctionDeclaration:
        return FunctionDeclaration(
            name=self.name,
            description=self.description,
            parameters=Schema(
                type=Type.OBJECT,
                properties={
                    "concept_name": Schema(
                        type=Type.STRING,
                        description="The name of the new concept, e.g., 'robotics club'.",
                    ),
                    "category_guess": Schema(
                        type=Type.STRING,
                        description="Your best guess for the category, e.g., 'Educational', 'Sports'."
                    )
                },
                required=["concept_name", "category_guess"],
            ),
        )

    async def run_async(self, *, args: dict, tool_context: ToolContext) -> str:
        concept_name = args.get("concept_name")
        category_guess = args.get("category_guess")

        if not concept_name or not category_guess:
            return "Error: Both concept_name and category_guess are required."

        try:
            # Access services via tool_context at runtime
            invocation_ctx = tool_context._invocation_context
            memory_service = invocation_ctx.memory_service

            if not memory_service:
                 return "Error: Memory service is not available to propose a new concept."

            # We need the base_url from the memory service to make the direct HTTP call
            base_url = getattr(memory_service, 'base_url', None)
            if not base_url:
                return "Error: Memory service is not configured with a base URL."

            async with httpx.AsyncClient() as client:
                payload = {
                    "name": concept_name,
                    "category_guess": category_guess
                }
                headers = {"X-User-ID": invocation_ctx.user_id}
                response = await client.post(f"{base_url}/propose_concept", json=payload, headers=headers)
                response.raise_for_status()
                return response.json().get("message", "Concept proposed successfully.")
        except Exception as e:
            logger.error(f"ERROR in propose_new_concept: {e}", exc_info=True)
            return f"An error occurred while proposing the new concept: {e}"
