import os
from dotenv import load_dotenv

# Load environment variables from a .env file in the project's root directory
load_dotenv()

# --- HELPER FUNCTION (MOVED OUTSIDE THE CLASS) ---
def _get_model_for_role(role: str, use_hybrid: bool, kimi_model: str, fallback_model: str, cheap_model: str) -> str:
    """Helper to select model based on role and hybrid configuration."""
    if not use_hybrid:
        return kimi_model
    
    role_map = {
        "orchestrator": kimi_model,
        "search": fallback_model,
        "response": fallback_model,
        "tool": cheap_model,
    }
    return role_map.get(role, fallback_model)

class AgentConfig:
    """Centralized configuration for the multi-tool agent."""

    # --- API Keys ---
    GOOGLE_API_KEY = os.environ.get("GOOGLE_API_KEY")
    NOVITA_API_KEY = os.environ.get("NOVITA_API_KEY")
    DEEPINFRA_API_KEY = os.environ.get("DEEPINFRA_API_KEY")

    # --- Qdrant Vector DB Configuration ---
    QDRANT_URL = os.environ.get("QDRANT_URL", "http://localhost:6333")
    QDRANT_API_KEY = os.environ.get("QDRANT_API_KEY") 
    QDRANT_COLLECTION_NAME = "community_activities_v2" # Updated for new schema

    # --- Neo4j and Graphiti (if used) ---
    NEO4J_URI = os.environ.get("NEO4J_URI", "neo4j://localhost:7687")
    NEO4J_USER = os.environ.get("NEO4J_USER", "neo4j")
    NEO4J_PASSWORD = os.environ.get("NEO4J_PASSWORD", "password")
    GRAPHITI_MODEL = os.environ.get("GRAPHITI_MODEL", "gemini-1.5-flash")

    # --- Model Configuration ---
    # **FIX: Add the 'openai/' prefix for all OpenAI-compatible endpoints.**
    # This tells LiteLLM to use the OpenAI client protocol.
    KIMI_MODEL = "openai/moonshotai/kimi-k2-instruct"
    NOVITA_API_BASE = "https://api.novita.ai/v3/openai"
    
    DEEPINFRA_KIMI_MODEL = "openai/moonshotai/Kimi-K2-Instruct"
    DEEPINFRA_API_BASE = "https://api.deepinfra.com/v1/openai"
    USE_DEEPINFRA_FALLBACK = True
    DEEPINFRA_CONCURRENT_LIMIT = 200  # 200 concurrent requests (not RPM)
    DEEPINFRA_EFFECTIVE_RPM = 4000    # Conservative estimate for 3s avg response time

    # **FIX: Add the 'vertex_ai/' prefix for Gemini models.**
    # This tells LiteLLM to use the Vertex AI client protocol.
    # **UPDATED to newer, more capable models**
    FALLBACK_MODEL = "vertex_ai/gemini-2.5-flash"
    CHEAP_MODEL = "vertex_ai/gemini-2.5-flash-lite-preview-06-17"

    # Balanced hybrid model strategy - Kimi for high-value tasks
    USE_HYBRID_MODELS = True             # Enable hybrid model usage
    KIMI_FOR_STRATEGIC_TASKS = True      # Use Kimi for final output, delegation, tool calling
    KIMI_FOR_REASONING_ONLY = True       # Limit Kimi to critical reasoning tasks only

    # --- Use the helper function to set class attributes ---
    ORCHESTRATOR_MODEL = _get_model_for_role("orchestrator", USE_HYBRID_MODELS, KIMI_MODEL, FALLBACK_MODEL, CHEAP_MODEL)
    SEARCH_AGENT_MODEL = _get_model_for_role("search", USE_HYBRID_MODELS, KIMI_MODEL, FALLBACK_MODEL, CHEAP_MODEL)
    RESPONSE_AGENT_MODEL = _get_model_for_role("response", USE_HYBRID_MODELS, KIMI_MODEL, FALLBACK_MODEL, CHEAP_MODEL)
    TOOL_AGENT_MODEL = _get_model_for_role("tool", USE_HYBRID_MODELS, KIMI_MODEL, FALLBACK_MODEL, CHEAP_MODEL)

    # --- Embedding Model Configuration for 3-Stage Hybrid Search ---
    # Local model cache directory for production
    MODEL_CACHE_DIR = os.environ.get("MODEL_CACHE_DIR", "./models")

    # Stage 1: Dense vector model for semantic meaning
    EMBEDDING_MODEL = "BAAI/bge-base-en-v1.5"
    EMBEDDING_DIM = 768

    # Stage 1: Sparse vector model for keyword matching
    SPARSE_EMBEDDING_MODEL = "Qdrant/bm25"

    # Stage 2: Late-interaction model for reranking
    COLBERT_MODEL = "colbert-ir/colbertv2.0"
    COLBERT_DIM = 128 # The dimension for ColBERT vectors

    # Model initialization settings
    PRELOAD_MODELS_ON_STARTUP = os.environ.get("PRELOAD_MODELS", "false").lower() == "true"
    MODEL_DOWNLOAD_TIMEOUT = 300  # 5 minutes timeout for model downloads

    # --- Advanced RAG Configuration ---
    # Binary quantization for ultra-fast retrieval (10-50x speed improvement)
    ENABLE_BINARY_QUANTIZATION = True

    # 3-Stage reranking funnel parameters
    STAGE1_CANDIDATE_MULTIPLIER = 6  # Reduced from 8 for faster Stage 1
    MIN_STAGE1_CANDIDATES = 30       # Reduced from 50 for faster processing
    RERANK_SCORE_THRESHOLD = 0.05    # Lowered threshold for more results

    # Performance optimization
    ENABLE_RESCORE = True            # Use rescoring with quantization for precision
    MAX_CONCURRENT_SEARCHES = 5      # Limit concurrent search operations

    # --- API Rate Limiting Configuration ---
    # Combined Kimi K2 capacity: Novita (10 RPM) + DeepInfra (200 concurrent = ~4000 RPM)
    ENABLE_API_OPTIMIZATION = False  # Disable optimization - we have massive capacity
    MAX_API_CALLS_PER_REQUEST = 10   # Allow many API calls for rich dialogue
    ENABLE_RESPONSE_CACHING = True   # Cache LLM responses for efficiency
    CACHE_TTL_MINUTES = 30           # Cache responses for 30 minutes
    ULTRA_LOW_API_MODE = False       # Disable ultra-low mode - we have abundant capacity

    # Rate limiting for Kimi K2 with dual providers (effectively unlimited for dialogue)
    # NOTE: Gemini models have NO rate limits - can use freely!
    KIMI_RATE_LIMIT_RPM = 10         # Novita primary limit (conservative)
    KIMI_MIN_INTERVAL_SECONDS = 1.0  # Minimal interval - DeepInfra handles overflow
    ENABLE_REQUEST_QUEUE = True      # Queue requests for Novita, overflow to DeepInfra
    MAX_QUEUE_SIZE = 100             # Increased queue size
    QUEUE_TIMEOUT_SECONDS = 60       # Longer timeout for complex conversations

    # Aggressive fallback strategy (Gemini has no limits!)
    USE_FALLBACK_ON_RATE_LIMIT = True # Use unlimited Gemini when Kimi is rate limited
    FALLBACK_RETRY_DELAY = 5         # Reduced delay since Gemini is unlimited
    MAX_FALLBACK_RETRIES = 1         # Fewer retries needed with unlimited fallbacks

    # Concurrent processing (since Gemini is unlimited)
    MAX_CONCURRENT_GEMINI_REQUESTS = 10  # Can process many Gemini requests simultaneously
    AGGRESSIVE_GEMINI_USAGE = True       # Use Gemini aggressively for non-critical tasks

    # Ultra-fast mode settings (for production)
    ULTRA_FAST_MODE = True           # Enable aggressive speed optimizations
    FAST_MODE_CANDIDATE_LIMIT = 20   # Even fewer candidates in ultra-fast mode
    CACHE_TTL_SECONDS = 600          # 10-minute cache for frequent queries

    # --- Conversation and Dialogue Configuration ---
    ENABLE_CONVERSATION = True       # Enable multi-turn dialogue capabilities
    ENABLE_CONTEXT_MEMORY = True     # Enable conversation context storage
    MAX_CONVERSATION_TURNS = 10      # Maximum turns in a single conversation
    CONVERSATION_TIMEOUT_MINUTES = 30 # Conversation context timeout
