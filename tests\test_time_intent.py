import pytest
from multi_tool_agent.utils.time_intent import classify_temporal_intent, TemporalIntent, get_weekday_list, get_weekend_list

class TestTemporalIntentClassification:
    """Test cases for temporal intent classification"""
    
    @pytest.mark.parametrize("query,expected_intent", [
        # Weekday patterns
        ("any activities for 5 year olds that invovling dancing on weekdays in Burnaby?", TemporalIntent.WEEKDAY_RANGE),
        ("weekday swimming classes", TemporalIntent.WEEKDAY_RANGE),
        ("classes on weekdays", TemporalIntent.WEEKDAY_RANGE),
        ("Monday and Tuesday classes", TemporalIntent.WEEKDAY_RANGE),
        ("every wednesday", TemporalIntent.WEEKDAY_RANGE),
        ("during the week activities", TemporalIntent.WEEKDAY_RANGE),
        
        # Weekend patterns
        ("weekend activities", TemporalIntent.WEEKEND),
        ("this weekend", TemporalIntent.WEEKEND),
        ("Saturday classes", TemporalIntent.WEEKEND),
        ("Sunday programs", TemporalIntent.WEEKEND),
        ("every weekend", TemporalIntent.WEEKEND),
        
        # Relative date patterns
        ("today's classes", TemporalIntent.RELATIVE_DATE),
        ("tomorrow's activities", TemporalIntent.RELATIVE_DATE),
        ("next week swimming", TemporalIntent.RELATIVE_DATE),
        ("this week's schedule", TemporalIntent.RELATIVE_DATE),
        
        # Exact date patterns
        ("activities on 2025-07-21", TemporalIntent.EXACT_DATE),
        ("classes for 2025-12-25", TemporalIntent.EXACT_DATE),
        
        # No temporal pattern
        ("swimming classes", TemporalIntent.NONE),
        ("dance activities", TemporalIntent.NONE),
        ("art programs for kids", TemporalIntent.NONE),
    ])
    def test_classify_temporal_intent(self, query, expected_intent):
        """Test that queries are classified with the correct temporal intent"""
        assert classify_temporal_intent(query) == expected_intent
    
    def test_get_weekday_list(self):
        """Test that weekday list is correct"""
        weekdays = get_weekday_list()
        assert len(weekdays) == 5
        assert weekdays == ["monday", "tuesday", "wednesday", "thursday", "friday"]
    
    def test_get_weekend_list(self):
        """Test that weekend list is correct"""
        weekend = get_weekend_list()
        assert len(weekend) == 2
        assert weekend == ["saturday", "sunday"]
    
    def test_case_insensitive(self):
        """Test that classification is case-insensitive"""
        assert classify_temporal_intent("WEEKDAYS") == TemporalIntent.WEEKDAY_RANGE
        assert classify_temporal_intent("This Weekend") == TemporalIntent.WEEKEND
        assert classify_temporal_intent("TOMORROW") == TemporalIntent.RELATIVE_DATE
