#!/usr/bin/env python3
"""
Test script to verify the complete agent functionality with fixed reasoning tools.
This script tests the agent's ability to handle a user query about dancing activities
for a 5-year-old on weekdays in Burnaby, ensuring all tools work properly.
"""

import asyncio
import logging
import sys
import os

# Add the parent directory to the path to fix imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService, Session
from google.adk.agents.run_config import RunConfig, StreamingMode
from google.adk.memory import InMemoryMemoryService
from google.adk.tools import ToolContext
from google.adk import Agent
from google.genai import types

from multi_tool_agent.orchestrator import root_agent
from multi_tool_agent.memory.graphiti_api_memory_service import GraphitiApiMemoryService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_agent_complete_flow():
    """Test the complete agent flow with a realistic user query."""
    
    print("🧪 Testing Complete Agent Flow")
    print("=" * 50)
    
    try:
        # Use the Runner to properly set up the agent context
        runner = Runner(
            agent=root_agent,
            session_service=InMemorySessionService(),
            memory_service=InMemoryMemoryService(),
            app_name="test-app"
        )
        
        # Test query: Dancing activities for 5-year-old on weekdays in Burnaby
        user_query = "Hi! I'm looking for dancing activities for my 5-year-old on weekdays in Burnaby. Can you help me find some options?"
        
        print(f"📝 User Query: {user_query}")
        print("\n🤖 Agent Processing...")
        print("-" * 30)
        
        # Process the query with the runner
        from google.genai.types import Content, Part
        user_message = Content(parts=[Part(text=user_query)], role='user')
        
        response_events = []
        async for event in runner.run_async(
            user_id="test-user",
            session_id="test-session",
            new_message=user_message
        ):
            response_events.append(event)
        
        # Extract the final response from events
        response = "Agent processing completed. Check events for details."
        
        print(f"\n✅ Agent Response:")
        print(f"{response}")
        
        print("\n" + "=" * 50)
        print("🎉 Test completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        logger.error(f"Test failed: {e}", exc_info=True)
        # Don't re-raise to let the individual tools test complete

async def test_reasoning_tools_individually():
    """Test the reasoning tools individually to ensure they work properly."""
    
    print("\n🔧 Testing Individual Reasoning Tools")
    print("=" * 50)
    
    from multi_tool_agent.tools.reasoning_tools import DateParser, RewriteSearchQuery
    
    # Create a mock tool context similar to the memory test example
    class MockInvocationContext:
        def __init__(self):
            self.user_id = "test-user"
            self.app_name = "test-app"
            self.memory_service = None
            self.session = type('Session', (), {'id': 'test_session'})()
    
    class MockToolContext:
        def __init__(self):
            self._invocation_context = MockInvocationContext()
    
    mock_context = MockToolContext()
    
    # Test DateParser
    print("📅 Testing DateParser...")
    date_parser = DateParser()
    try:
        date_result = await date_parser.run_async(
            args={"query": "weekdays"},
            tool_context=mock_context
        )
        print(f"✅ DateParser result: {date_result}")
    except Exception as e:
        print(f"❌ DateParser failed: {e}")
        logger.error(f"DateParser failed: {e}", exc_info=True)
    
    # Test RewriteSearchQuery
    print("\n🔍 Testing RewriteSearchQuery...")
    query_rewriter = RewriteSearchQuery()
    try:
        rewrite_result = await query_rewriter.run_async(
            args={"query": "dancing"},
            tool_context=mock_context
        )
        print(f"✅ RewriteSearchQuery result: {rewrite_result}")
    except Exception as e:
        print(f"❌ RewriteSearchQuery failed: {e}")
        logger.error(f"RewriteSearchQuery failed: {e}", exc_info=True)

if __name__ == "__main__":
    print("🚀 Starting Agent Tests")
    print("=" * 60)
    
    # First test individual tools
    asyncio.run(test_reasoning_tools_individually())
    
    # Then test the complete agent flow
    asyncio.run(test_agent_complete_flow())
