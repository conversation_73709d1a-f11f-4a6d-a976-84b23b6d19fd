"""
Direct Targon API client to bypass LiteLLM routing issues
"""

import aiohttp
import asyncio
import json
import logging
from typing import AsyncIterator, Dict, List, Any

logger = logging.getLogger(__name__)

class TargonClient:
    """Direct client for Targon API that bypasses LiteLLM routing."""
    
    def __init__(self, api_key: str, api_base: str = "https://api.targon.com/v1"):
        self.api_key = api_key
        self.api_base = api_base.rstrip('/')
        
    async def stream_completion(self, 
                              model: str,
                              messages: List[Dict[str, str]],
                              temperature: float = 0.7,
                              max_tokens: int = 4096) -> AsyncIterator[str]:
        """Stream completion from Targon API."""
        
        url = f"{self.api_base}/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": True
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=payload) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"Targon API error: {response.status} - {error_text}")
                
                async for line in response.content:
                    line = line.decode('utf-8').strip()
                    if line.startswith('data: '):
                        data_str = line[6:]  # Remove 'data: ' prefix
                        if data_str == '[DONE]':
                            break
                        
                        try:
                            data = json.loads(data_str)
                            if 'choices' in data and data['choices']:
                                choice = data['choices'][0]
                                if 'delta' in choice and 'content' in choice['delta']:
                                    content = choice['delta']['content']
                                    if content:
                                        yield content
                        except json.JSONDecodeError:
                            logger.warning(f"Failed to parse SSE data: {data_str}")
                            continue

# Global instance
_targon_client = None

def get_targon_client(api_key: str = None, api_base: str = None) -> TargonClient:
    """Get or create Targon client."""
    global _targon_client
    
    if _targon_client is None:
        from ..config import AgentConfig
        api_key = api_key or AgentConfig.TARGON_API_KEY
        api_base = api_base or AgentConfig.TARGON_API_BASE
        
        if not api_key:
            raise ValueError("TARGON_API_KEY not set")
            
        _targon_client = TargonClient(api_key, api_base)
        logger.info(f"✅ Initialized Targon client for {api_base}")
        
    return _targon_client
