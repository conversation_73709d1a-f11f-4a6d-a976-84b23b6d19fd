# multi_tool_agent/orchestrator.py

import logging
import json
from google.adk.agents import Agent
from google.adk.tools import google_search, BaseTool, ToolContext, FunctionTool
from google.adk.tools.agent_tool import AgentTool
from google.genai.types import FunctionDeclaration, Part, Content
from typing import Dict, Any

# --- Tool Imports ---
from .tools.scheduling_tools import find_daily_schedule, create_weekly_plan
from .tools.memory_tools import RetrieveInfoFromMemory
from .tools.current_date_tool import GetCurrentDate, GetWeekendDates
from .tools.conversation_tools import ConversationContextTool

# --- ADK Component Imports ---
from .config import AgentConfig
from .models.rate_limited_llm import create_ultra_optimized_model
from google.adk.planners import PlanReActPlanner

# --- Logging Setup ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

# --- ARCHITECTURE: Planner -> Specialist Tools ---
# We are simplifying the architecture. Instead of Planner -> AgentTool -> WorkerAgent -> Tool,
# we will do Planner -> WorkerTool. This is more direct and avoids schema inference issues.

# --- SPECIALIST AGENT (for fallback search) ---
WebSearchAgent = Agent(
    name="WebSearchAgent",
    model=AgentConfig.FALLBACK_MODEL,
    description="Performs a web search to find current information about local activities, locations, and events. Use as a fallback if other tools find nothing.",
    instruction="You are a web search expert. Use the google_search tool to find relevant information and provide a concise summary.",
    tools=[google_search]
)

# --- ULTRA-OPTIMIZED SINGLE AGENT (Legacy - for forced optimization only) ---
ultra_optimized_instruction = (
    "You are an ultra-optimized activity assistant designed for MAXIMUM SPEED and MINIMAL API usage.\n\n"
    "🎯 **CRITICAL OPTIMIZATION RULES (Legacy Mode):**\n"
    "- **SINGLE TOOL CALL ONLY** - Use ONE tool and provide complete response\n"
    "- **NO MEMORY CHECKS** - Skip memory retrieval to save API calls\n"
    "- **DIRECT SEARCH** - Go straight to activity search\n"
    "- **IMMEDIATE RESPONSE** - Provide complete answer after one tool call\n\n"
    "## ULTRA-FAST WORKFLOW:\n"
    "1. **Identify request type** (back-to-back activities = daily schedule)\n"
    "2. **Call find_daily_schedule** directly with the user's query\n"
    "3. **Format and return complete response** immediately\n\n"
    "## SPEED OPTIMIZATIONS:\n"
    "- **Skip memory checks** - Assume no prior context\n"
    "- **Skip date checks** - Use current date by default\n"
    "- **One tool call only** - Use find_daily_schedule for everything\n"
    "- **Complete response immediately** - Include all details in one response\n\n"
    "## AVAILABLE TOOLS (use ONE only):\n"
    "- **find_daily_schedule:** Use this for ALL activity requests\n\n"
    "**NOTE: This mode is legacy - with 25 RPM capacity, DialogueAgent is preferred**\n"
    "**CRITICAL: Make ONE tool call, then provide complete formatted response. NO additional calls!**"
)

# --- DIALOGUE-ENABLED AGENT INSTRUCTION (Optimized for Abundant Capacity) ---
dialogue_agent_instruction = (
    "You are a conversational family activity assistant with ABUNDANT API CAPACITY (~4000 RPM). Optimize for USER EXPERIENCE over API efficiency.\n\n"
    "🎯 **RICH DIALOGUE STRATEGY:**\n"
    "1. **PLANNING**: Assess information and plan comprehensive response\n"
    "2. **ACTION**: Use multiple tools as needed - memory, context, search, dates\n"
    "3. **REASONING**: Analyze results and provide rich, detailed responses\n"
    "4. **FINAL_ANSWER**: Comprehensive answer with follow-up suggestions\n\n"
    "## ENHANCED CONVERSATION FLOW:\n"
    "- **Rich Tool Usage**: Use multiple tools per turn for comprehensive responses\n"
    "- **Proactive Search**: Search even with partial info and ask for refinements\n"
    "- **Context Building**: Store detailed context for future turns\n"
    "- **Follow-up Suggestions**: Always suggest next steps or related options\n"
    "- **Comprehensive Answers**: Provide detailed information, not just basic responses\n\n"
    "## INFORMATION GATHERING STRATEGY:**\n"
    "1. **Essential**: Child's age, location, activity type\n"
    "2. **Valuable**: Schedule preferences, skill level, budget, transportation\n"
    "3. **Enriching**: Family interests, previous experiences, special needs\n\n"
    "## ENHANCED DIALOGUE EXAMPLES:**\n"
    "User: 'Looking for activities for my kid'\n"
    "PLANNING: Gather info AND provide helpful context about available options\n"
    "ACTION: Check memory, get current date, provide overview of available activities\n"
    "FINAL_ANSWER: 'I'd love to help! I have activities for children in New Westminster and Burnaby. How old is your child and what area works best? I can show you swimming, art, sports, and more!'\n\n"
    "User: 'Swimming for 5 year old'\n"
    "PLANNING: Search for swimming, ask for location, provide comprehensive options\n"
    "ACTION: Search swimming activities, get current date for scheduling\n"
    "FINAL_ANSWER: [Detailed swimming options] + 'What area works best for you? I can also find back-to-back classes or other activities if interested!'\n\n"
    "## AVAILABLE TOOLS (Use Multiple Per Turn):\n"
    "- **conversation_context:** Store/retrieve rich conversation context\n"
    "- **retrieve_info_from_memory:** Check user preferences and family context\n"
    "- **find_daily_schedule:** Find activities with detailed scheduling\n"
    "- **create_weekly_plan:** Create comprehensive weekly activity plans\n"
    "- **GetCurrentDate / GetWeekendDates:** Get current date for relevant scheduling\n"
    "- **WebSearchAgent:** Supplement with web search for additional options\n\n"
    "**With ~4000 RPM capacity: Use multiple tools, provide rich responses, be proactive!**\n"
    "**Focus on USER EXPERIENCE - comprehensive, helpful, engaging dialogue!**"
)

# --- REGULAR PLANNER AGENT (fallback) ---
regular_planner_instruction = (
    "You are a helpful family activity assistant. Your goal is to help parents find activities for their children.\n\n"
    "## YOUR PROCESS:\n"
    "1. **Always start** by calling `retrieve_info_from_memory` to check for user preferences and context.\n"
    "2. **Get current date** if the user mentions relative dates like 'today', 'this weekend', etc.\n"
    "3. **Search for activities** using the appropriate tool:\n"
    "   - Use `find_daily_schedule` for single-day requests or back-to-back activities\n"
    "   - Use `create_weekly_plan` for multi-day or weekly requests\n"
    "4. **Provide a comprehensive answer** with specific activity details, times, and locations.\n\n"
    "## AVAILABLE TOOLS:\n"
    "- **retrieve_info_from_memory:** Check for stored user preferences and family context\n"
    "- **find_daily_schedule:** Find activities for a specific day, including back-to-back options\n"
    "- **create_weekly_plan:** Find activities across multiple days or weeks\n"
    "- **GetCurrentDate / GetWeekendDates:** Get current date information\n"
    "- **WebSearchAgent:** Use as backup if other tools don't find results\n\n"
    "## RESPONSE FORMAT:\n"
    "- Be conversational and helpful\n"
    "- Provide specific details: times, locations, ages, costs\n"
    "- For back-to-back activities, clearly explain the scheduling and timing\n"
    "- Always include registration information when available\n\n"
    "Work through your tools step by step, then provide a complete, helpful response to the user."
)

# --- ULTRA-OPTIMIZED AGENT (Single API call) ---
UltraOptimizedAgent = Agent(
    name="UltraOptimizedAgent",
    model=create_ultra_optimized_model(),
    description="Ultra-fast single-call agent for minimal API usage (10 RPM limit).",
    instruction=ultra_optimized_instruction,
    tools=[
        # ONLY the essential tool to force single call
        FunctionTool(func=find_daily_schedule),
    ]
)

# --- DIALOGUE-ENABLED AGENT (Multi-turn conversation) ---
DialogueAgent = Agent(
    name="DialogueAgent",
    model=create_ultra_optimized_model(),
    description="Conversational agent designed for multi-turn dialogue and natural conversation flow.",
    instruction=dialogue_agent_instruction,
    tools=[
        ConversationContextTool(),
        RetrieveInfoFromMemory(),
        FunctionTool(func=find_daily_schedule),
        FunctionTool(func=create_weekly_plan),
        AgentTool(agent=WebSearchAgent, skip_summarization=True),
        GetCurrentDate(),
        GetWeekendDates(),
    ]
)

# --- REGULAR AGENT (Multiple tool calls allowed) ---
RegularAgent = Agent(
    name="RegularOrchestrator",
    model=create_ultra_optimized_model(),
    description="Regular orchestrator with full tool access for comprehensive responses.",
    instruction=regular_planner_instruction,
    tools=[
        RetrieveInfoFromMemory(),
        FunctionTool(func=find_daily_schedule),
        FunctionTool(func=create_weekly_plan),
        AgentTool(agent=WebSearchAgent, skip_summarization=True),
        GetCurrentDate(),
        GetWeekendDates(),
    ]
)

# --- INTELLIGENT AGENT SELECTION SYSTEM ---
# With massive capacity (Novita 10 RPM + DeepInfra 200 concurrent = ~4000 RPM), optimize for UX
if AgentConfig.ENABLE_CONVERSATION:
    root_agent = DialogueAgent
    logger.info("💬 Using DialogueAgent - Multi-turn conversation enabled (~4000 RPM capacity)")
elif AgentConfig.ULTRA_LOW_API_MODE:
    # Only use ultra-optimized mode if explicitly forced (legacy)
    root_agent = UltraOptimizedAgent
    logger.info("⚡ Using UltraOptimizedAgent - MAXIMUM 1 API call per request (legacy forced mode)")
else:
    # Default to regular agent - we have abundant capacity now
    root_agent = RegularAgent
    logger.info("🔄 Using RegularAgent with full tool access (~4000 RPM capacity)")
