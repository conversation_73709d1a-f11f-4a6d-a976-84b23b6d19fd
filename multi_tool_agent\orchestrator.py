# multi_tool_agent/orchestrator.py

import logging
import json
from google.adk.agents import Agent
from google.adk.tools import google_search, BaseTool, ToolContext, FunctionTool
from google.adk.tools.agent_tool import AgentTool
from google.genai.types import FunctionDeclaration, Part, Content
from typing import Dict, Any

# --- Tool Imports ---
from .tools.scheduling_tools import find_daily_schedule, create_weekly_plan
from .tools.memory_tools import RetrieveInfoFromMemory
from .tools.current_date_tool import GetCurrentDate, GetWeekendDates
from .tools.conversation_tools import ConversationContextTool

# --- ADK Component Imports ---
from .config import AgentConfig
from .models.rate_limited_llm import create_ultra_optimized_model
from google.adk.planners import PlanReActPlanner

# --- Logging Setup ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

# --- ARCHITECTURE: Planner -> Specialist Tools ---
# We are simplifying the architecture. Instead of Planner -> AgentTool -> WorkerAgent -> Tool,
# we will do Planner -> WorkerTool. This is more direct and avoids schema inference issues.

# --- SPECIALIST AGENT (for fallback search) ---
WebSearchAgent = Agent(
    name="WebSearchAgent",
    model=AgentConfig.FALLBACK_MODEL,
    description="Performs a web search to find current information about local activities, locations, and events. Use as a fallback if other tools find nothing.",
    instruction="You are a web search expert. Use the google_search tool to find relevant information and provide a concise summary.",
    tools=[google_search]
)

# --- ULTRA-OPTIMIZED SINGLE AGENT FOR MINIMAL API USAGE ---
ultra_optimized_instruction = (
    "You are an ultra-optimized activity assistant designed for MAXIMUM SPEED and MINIMAL API usage.\n\n"
    "🎯 **CRITICAL OPTIMIZATION RULES:**\n"
    "- **SINGLE TOOL CALL ONLY** - Use ONE tool and provide complete response\n"
    "- **NO MEMORY CHECKS** - Skip memory retrieval to save API calls\n"
    "- **DIRECT SEARCH** - Go straight to activity search\n"
    "- **IMMEDIATE RESPONSE** - Provide complete answer after one tool call\n\n"
    "## ULTRA-FAST WORKFLOW:\n"
    "1. **Identify request type** (back-to-back activities = daily schedule)\n"
    "2. **Call find_daily_schedule** directly with the user's query\n"
    "3. **Format and return complete response** immediately\n\n"
    "## SPEED OPTIMIZATIONS:\n"
    "- **Skip memory checks** - Assume no prior context\n"
    "- **Skip date checks** - Use current date by default\n"
    "- **One tool call only** - Use find_daily_schedule for everything\n"
    "- **Complete response immediately** - Include all details in one response\n\n"
    "## AVAILABLE TOOLS (use ONE only):\n"
    "- **find_daily_schedule:** Use this for ALL activity requests\n\n"
    "**CRITICAL: Make ONE tool call, then provide complete formatted response. NO additional calls!**"
)

# --- DIALOGUE-ENABLED AGENT INSTRUCTION ---
dialogue_agent_instruction = (
    "You are a conversational family activity assistant designed for MULTI-TURN DIALOGUE. Your goal is to help parents find activities through natural conversation.\n\n"
    "🎯 **DIALOGUE STRATEGY:**\n"
    "1. **Identify missing critical info** (child's age, location, budget, specific interests)\n"
    "2. **Ask ONE clarifying question** if essential information is missing\n"
    "3. **Use tools efficiently** - combine memory + scheduling in single call when possible\n"
    "4. **Provide partial suggestions** with 'Would you like me to find more options?'\n"
    "5. **Remember context** from previous turns in the conversation\n\n"
    "## CONVERSATION FLOW:\n"
    "- **First turn**: Check memory, ask for missing critical info if needed\n"
    "- **Follow-up turns**: Build on previous context, refine search based on feedback\n"
    "- **Always be helpful**: Provide useful partial results even with incomplete info\n\n"
    "## CRITICAL INFO HIERARCHY:**\n"
    "1. **Essential**: Child's age (approximate is fine), general location\n"
    "2. **Important**: Activity type, day/time preferences\n"
    "3. **Nice to have**: Budget, specific venues, transportation needs\n\n"
    "## DIALOGUE EXAMPLES:**\n"
    "User: 'Looking for activities for my kid'\n"
    "You: 'I'd love to help! How old is your child and what area are you looking in?'\n\n"
    "User: 'Swimming for 5 year old'\n"
    "You: 'Great! What city or area would work best for you? I can find swimming lessons in New Westminster and Burnaby.'\n\n"
    "## AVAILABLE TOOLS:\n"
    "- **retrieve_info_from_memory:** Check for stored user preferences and family context\n"
    "- **find_daily_schedule:** Find activities for a specific day, including back-to-back options\n"
    "- **create_weekly_plan:** Find activities across multiple days or weeks\n"
    "- **GetCurrentDate / GetWeekendDates:** Get current date information\n"
    "- **WebSearchAgent:** Use as backup if other tools don't find results\n\n"
    "**Be conversational, ask clarifying questions, and build on previous context!**"
)

# --- REGULAR PLANNER AGENT (fallback) ---
regular_planner_instruction = (
    "You are a helpful family activity assistant. Your goal is to help parents find activities for their children.\n\n"
    "## YOUR PROCESS:\n"
    "1. **Always start** by calling `retrieve_info_from_memory` to check for user preferences and context.\n"
    "2. **Get current date** if the user mentions relative dates like 'today', 'this weekend', etc.\n"
    "3. **Search for activities** using the appropriate tool:\n"
    "   - Use `find_daily_schedule` for single-day requests or back-to-back activities\n"
    "   - Use `create_weekly_plan` for multi-day or weekly requests\n"
    "4. **Provide a comprehensive answer** with specific activity details, times, and locations.\n\n"
    "## AVAILABLE TOOLS:\n"
    "- **retrieve_info_from_memory:** Check for stored user preferences and family context\n"
    "- **find_daily_schedule:** Find activities for a specific day, including back-to-back options\n"
    "- **create_weekly_plan:** Find activities across multiple days or weeks\n"
    "- **GetCurrentDate / GetWeekendDates:** Get current date information\n"
    "- **WebSearchAgent:** Use as backup if other tools don't find results\n\n"
    "## RESPONSE FORMAT:\n"
    "- Be conversational and helpful\n"
    "- Provide specific details: times, locations, ages, costs\n"
    "- For back-to-back activities, clearly explain the scheduling and timing\n"
    "- Always include registration information when available\n\n"
    "Work through your tools step by step, then provide a complete, helpful response to the user."
)

# --- ULTRA-OPTIMIZED AGENT (Single API call) ---
UltraOptimizedAgent = Agent(
    name="UltraOptimizedAgent",
    model=create_ultra_optimized_model(),
    description="Ultra-fast single-call agent for minimal API usage (10 RPM limit).",
    instruction=ultra_optimized_instruction,
    tools=[
        # ONLY the essential tool to force single call
        FunctionTool(func=find_daily_schedule),
    ]
)

# --- DIALOGUE-ENABLED AGENT (Multi-turn conversation) ---
DialogueAgent = Agent(
    name="DialogueAgent",
    model=create_ultra_optimized_model(),
    description="Conversational agent designed for multi-turn dialogue and natural conversation flow.",
    instruction=dialogue_agent_instruction,
    tools=[
        ConversationContextTool(),
        RetrieveInfoFromMemory(),
        FunctionTool(func=find_daily_schedule),
        FunctionTool(func=create_weekly_plan),
        AgentTool(agent=WebSearchAgent, skip_summarization=True),
        GetCurrentDate(),
        GetWeekendDates(),
    ]
)

# --- REGULAR AGENT (Multiple tool calls allowed) ---
RegularAgent = Agent(
    name="RegularOrchestrator",
    model=create_ultra_optimized_model(),
    description="Regular orchestrator with full tool access for comprehensive responses.",
    instruction=regular_planner_instruction,
    tools=[
        RetrieveInfoFromMemory(),
        FunctionTool(func=find_daily_schedule),
        FunctionTool(func=create_weekly_plan),
        AgentTool(agent=WebSearchAgent, skip_summarization=True),
        GetCurrentDate(),
        GetWeekendDates(),
    ]
)

# --- CONFIGURATION-BASED AGENT SELECTION ---
if AgentConfig.ENABLE_API_OPTIMIZATION and AgentConfig.ULTRA_LOW_API_MODE:
    root_agent = UltraOptimizedAgent
    logger.info("⚡ Using UltraOptimizedAgent - MAXIMUM 1 API call per request (10 RPM limit)")
else:
    root_agent = RegularAgent
    logger.info("🔄 Using RegularAgent with full tool access")
