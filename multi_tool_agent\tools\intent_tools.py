# multi_tool_agent/tools/intent_tools.py
import logging
import json
from google.adk.tools import <PERSON>Tool, ToolContext
from google.genai.types import FunctionDeclaration, Schema, Type

from ..utils.time_intent import classify_temporal_intent, TemporalIntent, get_weekday_list, get_weekend_list

logger = logging.getLogger(__name__)

class TemporalIntentClassifierTool(BaseTool):
    """
    A fast, deterministic tool to classify the user's temporal intent.
    This helps the orchestrator agent decide which date/scheduling tool to use.
    """
    def __init__(self):
        super().__init__(
            name="classify_temporal_intent",
            description="Analyzes the user's query to determine if they are asking about a specific date, a relative date, weekdays, or the weekend."
        )

    def _get_declaration(self) -> FunctionDeclaration:
        return FunctionDeclaration(
            name=self.name,
            description=self.description,
            parameters=Schema(
                type=Type.OBJECT,
                properties={
                    "query": Schema(
                        type=Type.STRING,
                        description="The user's full, original query text.",
                    )
                },
                required=["query"],
            ),
        )

    async def run_async(self, *, args: dict, tool_context: ToolContext) -> dict:
        """
        Classifies the query and returns a structured response.
        """
        query = args.get("query")
        if not query:
            return {"error": "Query is required."}
        
        intent = classify_temporal_intent(query)
        
        response = {"intent": intent.value}
        
        # Add the appropriate day list based on intent
        if intent == TemporalIntent.WEEKDAY_RANGE:
            response["days_of_week"] = get_weekday_list()
        elif intent == TemporalIntent.WEEKEND:
            response["days_of_week"] = get_weekend_list()
        
        logger.info(f"🕐 Temporal intent classified: {intent.value} for query: '{query}'")
        
        # Return dict directly - ADK will handle JSON serialization
        return response
