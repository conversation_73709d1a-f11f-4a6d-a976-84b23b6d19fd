# Community Activity Finder Agent

This project is a sophisticated AI system designed to help users find and explore community activities. It understands complex natural language queries, including back-to-back scheduling, and provides accurate results from a real-time database using hybrid vector search.

## 🚀 Key Features

- **Multi-Child Scheduling Assistant**: Sophisticated AI agent that creates convenient daily and weekly schedules for multiple children with different ages.
- **Advanced Scheduling Intelligence**:
    - **Cross-Facility Planning**: Finds back-to-back activities across different facilities with travel time consideration.
    - **Same-Facility Optimization**: Identifies simultaneous class opportunities at the same location for maximum convenience.
    - **Activity Compatibility Validation**: Prevents invalid combinations (e.g., multiple levels of the same activity type).
    - **Balanced Multi-Age Search**: Eliminates search bias by running separate, age-appropriate searches for each child.
- **Hybrid Search Capabilities**:
    - **Semantic + Keyword Search**: Combines dense vectors for semantic understanding with sparse vectors for keyword matching.
    - **Advanced Filtering**: Supports multiple ages, location, day of week, price, and date filtering.
    - **Travel Time Intelligence**: Real-time travel time estimation between facilities.
- **High-Performance Architecture**: Built on Google's Agent Development Kit (ADK) using a direct-function-calling orchestrator for speed and accuracy.
- **Automated Cloud Deployment**: A single, robust script handles dependencies, builds, and deploys the entire agent and UI to Google Cloud Run.

---

## 🧠 Multi-Child Scheduling System

### Overview

The agent has been transformed from a simple data retriever into a sophisticated scheduling assistant that can plan convenient daily and weekly schedules for multiple children with different ages. This system addresses the real-world challenge of coordinating activities for families with children of varying ages.

### Key Scheduling Capabilities

#### 1. **Multi-Age Schema Support**
- **Before**: Single `age: int` field limited to one child
- **After**: `age: List[int]` field supports multiple children (e.g., `[3, 6]`)
- **Impact**: Agent can now understand and process requests for multiple children simultaneously

#### 2. **Balanced Multi-Age Search**
- **Problem Solved**: Search bias where results favored older children
- **Solution**: Separate age-specific searches with tailored query modifications
- **Age-Specific Query Enhancement**:
  - Ages 2-4: `"beginner preschool caregiver parent toddler intro"`
  - Ages 5-8: `"children level 1 level 2 elementary beginner intermediate"`
  - Ages 9+: `"advanced level 3 level 4 intermediate youth"`
- **Result**: Perfect balance ensuring equal representation for all children

#### 3. **Cross-Facility Scheduling Intelligence**
- **Travel Time Matrix**: Real-time estimation between facilities
- **Facilities Covered**: təməsew̓txʷ Aquatic Centre, Canada Games Pool, Century House, Queensborough Community Centre, Anvil Centre, Moody Park Arena
- **Smart Gap Analysis**: Considers travel time when evaluating back-to-back opportunities
- **Maximum Gap**: 3-hour window for realistic scheduling

#### 4. **Activity Compatibility Validation**
- **Rule Engine**: Prevents invalid activity combinations
- **Key Rules**:
  - Child cannot take multiple levels of the same activity type
  - Different activity types are always compatible
  - Non-leveled activities can be combined with anything
  - Duplicate bookings are prevented
- **Example**: `Swimming Level 1 + Gymnastics Level 3` ✅ | `Swimming Level 1 + Swimming Level 2` ❌

#### 5. **Sophisticated Scheduling Tools**

##### `find_daily_schedule()`
- **Purpose**: Creates convenient daily schedules for multiple children
- **Parameters**: `ages: List[int]`, `location: str`, `activity_keywords: List[str]`, `date: str`
- **Returns**: Structured scheduling opportunities with:
  - `back_to_back`: Sequential activities with travel time consideration
  - `simultaneous`: Overlapping activities at the same facility
  - Travel time estimates for cross-facility planning

##### `create_weekly_plan()`
- **Purpose**: Orchestrates comprehensive weekly scheduling
- **Functionality**: Runs daily schedule searches for entire week
- **Output**: Complete weekly schedule with opportunity counts per day

##### `get_travel_time()`
- **Purpose**: Provides real-time travel time estimates
- **Coverage**: Comprehensive facility-to-facility matrix
- **Integration**: Automatically called for cross-facility scheduling suggestions

### Architecture Components

#### Updated File Structure
```
multi_tool_agent/tools/
├── schemas.py               # Updated with List[int] age support
├── activity_search_tools.py # Balanced multi-age search & data retrieval
├── scheduling_tools.py      # Core scheduling intelligence
├── location_tools.py        # Travel time estimation
└── orchestrator.py          # Enhanced with scheduling-focused prompts
```

#### Data Flow
```mermaid
graph TD
    A["User: 'Find activities for ages 3 and 6'"] --> B[Orchestrator Agent]
    B --> C[find_daily_schedule Tool]
    C --> D[raw_activity_search]
    D --> E[Balanced Multi-Age Search]
    E --> F[Age 3: 'gymnastics beginner preschool']
    E --> G[Age 6: 'gymnastics children level 1']
    F --> H[Activities for Age 3]
    G --> I[Activities for Age 6]
    H --> J[Scheduling Analysis]
    I --> J
    J --> K[Activity Compatibility Check]
    K --> L[Travel Time Calculation]
    L --> M[Scheduling Opportunities]
    M --> N["Back-to-Back & Simultaneous Options"]
```

### Performance Improvements

#### Search Quality
- **Before**: Age bias led to 90% results for older child, 10% for younger
- **After**: Perfect 50/50 split with balanced representation
- **Method**: Separate searches with age-appropriate query modifications

#### Scheduling Intelligence
- **Before**: Simple back-to-back search at same facility only
- **After**: Cross-facility planning with travel time consideration
- **Coverage**: 6 major facilities with comprehensive travel matrix

#### Compatibility Validation
- **Before**: No validation of activity combinations
- **After**: Sophisticated rule engine prevents invalid combinations
- **Impact**: Parents can't accidentally book conflicting activities

### Real-World Example

**Query**: "Find gymnastics activities for my 3-year-old and 6-year-old in New Westminster"

**System Response**:
1. **Balanced Search**: Separate searches for ages 3 and 6
2. **Results**: 75 activities per age group (150 total)
3. **Scheduling Analysis**: 43 scheduling opportunities found
4. **Recommendations**:
   - **Simultaneous**: 7 opportunities at same facility, same time
   - **Back-to-Back**: 36 opportunities with travel time consideration
   - **Cross-Facility**: Travel times provided for different locations

### Debug and Testing

#### Debug Script
```bash
cd agent_google
python debug_qdrant_search.py --query "gymnastics" --ages 3 6 --location "New Westminster" --back-to-back
```

#### Test Results
- ✅ **Balanced Search**: 150 activities (75 per age)
- ✅ **Age-Specific Queries**: Tailored search terms applied
- ✅ **Travel Time**: Calculated between all facilities
- ✅ **Scheduling Opportunities**: 43 valid combinations found
- ✅ **Compatibility**: Invalid combinations filtered out

### Implementation Benefits

1. **No More Search Bias**: Equal representation for all children
2. **Cross-Facility Intelligence**: Plans activities across different locations
3. **Time-Aware Scheduling**: Considers travel time in recommendations
4. **Compatibility Validation**: Prevents booking conflicts
5. **Comprehensive Planning**: Daily and weekly scheduling tools
6. **Parent-Friendly**: Focuses on convenience and feasibility

---

## 🧠 Memory Layer Construction & Findings

### Overview

The agent has been enhanced with a sophisticated memory system using **Graphiti Core** - a knowledge graph-based memory solution that provides persistent, contextual memory across conversations. This implementation transforms the agent from a stateless query processor into a memory-aware assistant that learns and remembers user interactions.

### Memory Architecture

#### Core Components

```mermaid
graph TD
    A[User Interaction] --> B[ADK Memory Service]
    B --> C[GraphitiApiMemoryService]
    C --> D[Graphiti API Server]
    D --> E[Graphiti Core]
    E --> F[Neo4j Knowledge Graph]
    E --> G[Google Gemini LLM]
    E --> H[Google Embeddings]
    F --> I[Entities & Relationships]
    G --> J[Fact Extraction]
    H --> K[Semantic Search]
```

#### 1. **ADK Memory Service Integration**
- **File**: `multi_tool_agent/memory/graphiti_api_memory_service.py`
- **Purpose**: Adapter between Google ADK framework and Graphiti Core
- **Key Methods**:
  - `add_session_to_memory()`: Stores conversation sessions
  - `search_memory()`: Retrieves relevant facts using semantic search
- **Response Format**: Returns `SearchMemoryResponse` with `memories` field

#### 2. **Graphiti API Server**
- **File**: `api/main.py`
- **Technology**: FastAPI server providing RESTful interface
- **Endpoints**:
  - `POST /add_episode`: Adds new memory episodes to knowledge graph
  - `GET /search`: Searches memory using semantic queries
  - `GET /docs`: API documentation
- **Configuration**: Uses Google Gemini for LLM and embedding services

#### 3. **Knowledge Graph Storage**
- **Database**: Neo4j graph database (dockerized)
- **Connection**: `bolt://localhost:7687`
- **Authentication**: `neo4j/password`
- **Schema**: Entities, relationships, and facts with temporal information
- **Constraints**: Prevents duplicate entities with same name/group_id

#### 4. **Google AI Integration**
- **LLM Model**: `gemini-2.0-flash` for fact extraction and reasoning
- **Embedding Model**: `text-embedding-004` for semantic search
- **Cross-Encoder**: `gemini-2.5-flash-lite-preview` for result reranking
- **API Key**: Configured via `GOOGLE_API_KEY` environment variable

### Implementation Details

#### Memory Service Architecture

```python
class GraphitiApiMemoryService(BaseMemoryService):
    """ADK Memory Service adapter for Graphiti Core"""
    
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.http_client = httpx.AsyncClient()
        
    async def search_memory(self, *, app_name: str, user_id: str, query: str) -> SearchMemoryResponse:
        """Searches memory with user-scoped and public data"""
        params = {
            "query": query, 
            "group_ids": [user_id, "bc_local_user"]  # User + public data
        }
        response = await self.http_client.get(f"{self.base_url}/search", params=params)
        facts = [fact.get("fact", "") for fact in response.json().get("facts", [])]
        return SearchMemoryResponse(memories=facts)
```

#### Episode Processing Pipeline

1. **Episode Creation**: User interactions converted to structured episodes
2. **Fact Extraction**: Gemini extracts entities and relationships
3. **Embedding Generation**: Text-embedding-004 creates semantic vectors
4. **Graph Storage**: Facts stored as nodes/edges in Neo4j
5. **Indexing**: Semantic search indices updated for retrieval

### Memory System Findings

#### ✅ **Successful Implementation**

1. **Memory Persistence**: Episodes successfully stored in Neo4j knowledge graph
2. **Semantic Search**: Retrieves relevant facts using embedding similarity
3. **Entity Recognition**: Correctly identifies and links entities (Steven, Emma, Liam)
4. **Relationship Mapping**: Establishes family relationships and location data
5. **Temporal Awareness**: Tracks when facts were learned and their validity

#### ✅ **Test Results**

**Memory Addition Test:**
```
✅ Episode added successfully!
   Response: {'status': 'success', 'episode_id': 'bb536978-78ce-4e5e-bd60-45eb9d7cb559'}
```

**Memory Retrieval Test:**
```
✅ Search completed!
   Found 5 facts:
   1. I have two children: Emma
   2. I have two children: ... and Liam
   3. We live in New Westminster.
   4. We live in New Westminster.
   5. We live in New Westminster.
```

#### ⚠️ **Constraint Behavior (Expected)**

**Duplicate Entity Prevention:**
- Graphiti enforces uniqueness constraints on entities
- Prevents duplicate nodes with same `name` + `group_id`
- Error: `Node already exists with label 'Entity' and properties 'name' = 'Steven'`
- **Impact**: Maintains data integrity, prevents memory pollution

#### 🔧 **Technical Challenges Resolved**

1. **Timeout Issues**: Memory operations take 30-60 seconds due to LLM processing
   - **Solution**: Increased HTTP timeout to 60 seconds
   - **Cause**: Gemini fact extraction and embedding generation

2. **API Response Format**: Initial confusion about `memories` vs `results` field
   - **Solution**: Confirmed correct usage of `memories` field
   - **Status**: Already implemented correctly

3. **Server Startup**: Graphiti API server configuration and dependencies
   - **Solution**: Proper virtual environment activation and package installation
   - **Dependencies**: `graphiti-core`, `neo4j`, `fastapi`, `uvicorn`

4. **Neo4j Connectivity**: Database connection and authentication
   - **Solution**: Verified Docker container status and credentials
   - **Status**: Running on `bolt://localhost:7687` with `neo4j/password`

### Integration with ADK Framework

#### Server Configuration

```python
# adk_server.py - Memory service initialization
if os.getenv("GOOGLE_API_KEY"):
    memory_service = GraphitiApiMemoryService("http://localhost:8000")
    print("✅ Graphiti memory service initialized")
```

#### Memory-Aware Agent

- **Agent Type**: BC Activity Assistant with memory capabilities
- **Memory Context**: Searches user history for relevant information
- **Personalization**: Tailors responses based on remembered preferences
- **Continuity**: Maintains conversation context across sessions

### Memory System Benefits

1. **Persistent Learning**: Remembers user preferences and family details
2. **Contextual Responses**: Provides personalized activity recommendations
3. **Relationship Awareness**: Understands family structures and connections
4. **Temporal Intelligence**: Tracks when information was learned
5. **Scalable Storage**: Graph database handles complex relationship queries
6. **Semantic Search**: Finds relevant facts even with different wording

### Deployment Requirements

#### Environment Variables
```bash
# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password

# Google API Configuration
GOOGLE_API_KEY=your_api_key_here
GRAPHITI_MODEL=gemini-2.0-flash
EMBEDDING_MODEL=text-embedding-004
```

#### Service Startup
1. **Start Neo4j**: `docker run -d --name neo4j -p 7687:7687 -p 7474:7474 neo4j:latest`
2. **Activate Environment**: `.venv\Scripts\Activate.ps1`
3. **Start Graphiti API**: `cd api && python -m uvicorn main:app --host 0.0.0.0 --port 8000`
4. **Start ADK Server**: `python adk_server.py`

### Future Enhancements

1. **Memory Consolidation**: Periodic fact merging and deduplication
2. **Privacy Controls**: User-specific memory deletion and privacy settings
3. **Memory Analytics**: Insights into learned patterns and preferences
4. **Cross-User Learning**: Shared public knowledge while maintaining privacy
5. **Memory Visualization**: Graph-based interface for exploring stored knowledge

---

## 🏗️ Current System Architecture

The system has been simplified to a streamlined, high-performance architecture:

### Core Components

```mermaid
graph TD
    A[User Query] --> B[Orchestrator Agent]
    B --> C[fast_activity_search Tool]
    C --> D[Hybrid Qdrant Search]
    D --> E[Dense + Sparse Vectors]
    E --> F[Structured Results]
    F --> G[Formatted Response]
```

### 1. Orchestrator Agent (`multi_tool_agent/orchestrator.py`)
- **Purpose**: Main agent that understands user intent and extracts structured filters
- **Capabilities**: 
  - Parses natural language into search queries and filters
  - Handles back-to-back class requests
  - Routes to appropriate tools
- **Model**: Gemini 1.5 Flash for fast, accurate parsing

### 2. Fast Activity Search Tool (`multi_tool_agent/tools/activity_search_tools.py`)
- **Purpose**: Performs hybrid search with direct result formatting
- **Capabilities**:
  - Combines dense (semantic) and sparse (keyword) vector search
  - Applies structured filters at database level
  - Finds back-to-back class pairs
  - Returns formatted markdown directly (skips expensive LLM calls)
- **Performance**: Sub-second response times

### 3. Hybrid Qdrant Database
- **Dense Vectors**: BAAI/bge-small-en-v1.5 for semantic understanding
- **Sparse Vectors**: Qdrant/bm25 for keyword matching
- **ColBERT Vectors**: sentence-transformers/ColBERT-small for fine-grained matching (optional)
- **Payload Indexes**: Lightning-fast filtering on all fields
- **Data**: ~10,000+ activities from Burnaby and New Westminster
- **Vector Configuration**: Uses named vectors ("dense", "sparse", "colbert") for multi-vector search

### 4. Data Pipeline
- **Source**: Automated scraping from city recreation websites
- **Storage**: `community_activities.jsonl` (current data)
- **Ingestion**: `update_qdrant_activities.py` (hybrid vector generation)
- **Setup**: `setup_qdrant_collection.py` (collection configuration)

---

## 📁 Current File Structure

```
agent_google/
├── multi_tool_agent/
│   ├── orchestrator.py          # Main orchestrator agent
│   ├── run_agent.py             # Agent runner
│   ├── config.py                # System configuration
│   ├── update_qdrant_activities.py  # Data ingestion
│   ├── models/
│   │   └── rate_limited_llm.py  # Rate-limited LLM with provider fallback
│   ├── utils/
│   │   ├── rate_limiter.py      # Rate limiting and hybrid model management
│   │   ├── api_monitor.py       # API usage monitoring
│   │   └── request_queue.py     # Request queuing system
│   ├── tools/
│   │   ├── activity_search_tools.py  # Core hybrid search & balanced multi-age search
│   │   ├── scheduling_tools.py       # Multi-child scheduling intelligence
│   │   ├── location_tools.py         # Travel time estimation between facilities
│   │   ├── schemas.py                # Data schemas with multi-age support
│   │   └── __init__.py
│   ├── ingestion/
│   │   └── setup_qdrant_collection.py  # Qdrant setup
│   └── subagents/
│       └── __init__.py          # Placeholder agents
├── community_activities.jsonl   # Current activity data
├── test_back_to_back.py         # Test script
├── test_state_pollution_fix.py  # State pollution fix verification
├── requirements.txt             # Dependencies
└── archive/                     # Archived development files
```

---

## ⚙️ How to Run

### Step 1: Set Up Your Environment
1. **Clone the repository.**
2. **Create and activate a Python virtual environment.**
    ```bash
    python -m venv .venv
    source .venv/bin/activate  # On Windows, use `.venv\Scripts\activate`
    ```
3. **Install the required dependencies.**
    ```bash
    pip install -r requirements.txt
    ```
4. **Set up your environment variables.** Create a `.env` file in the project root:
    ```
    GOOGLE_API_KEY="your_google_api_key"
    QDRANT_URL="your_qdrant_cloud_url"
    QDRANT_API_KEY="your_qdrant_api_key"
    ```

### Step 2: Set Up Qdrant Collection
```bash
python multi_tool_agent/ingestion/setup_qdrant_collection.py
```

### Step 3: Ingest Activity Data
```bash
python multi_tool_agent/update_qdrant_activities.py
```

### Step 4: Test the System
```bash
python test_back_to_back.py
```

### Step 5: Run the Agent
```bash
python multi_tool_agent/run_agent.py
```

---

## 🔧 Critical Fix: State Pollution in LLM Provider Fallback Chain

### Problem Identified
The agent was experiencing a critical state pollution issue where API base URLs from one provider would leak into subsequent provider calls, causing 404 errors and failed fallbacks. This occurred when:

1. **Novita Kimi K2** was attempted first (rate-limited at 10 RPM)
2. **DeepInfra Kimi** was attempted as fallback 
3. **Gemini** was attempted as final fallback

The issue manifested as:
```
ERROR: Client error '404 Not Found' for url 'https://api.novita.ai/v3/openai:streamGenerateContent?alt=sse'
```

This showed that the Gemini call was incorrectly using Novita's API base URL.

### Root Cause Analysis
The problem was in the `RateLimitedLiteLlm` class where:

1. **Shared LiteLlm Instance**: The same `LiteLlm` instance was being reused across multiple provider attempts
2. **State Pollution**: LiteLLM's internal HTTP clients cached the `api_base` from previous calls
3. **Monkey-Patching Failures**: Attempts to override methods on existing instances didn't properly bind to the new context
4. **Method Signature Mismatch**: The ADK framework expected specific method signatures that weren't properly maintained

### Solution Architecture: Isolated LiteLlm Instances

We implemented a **controller pattern** where `RateLimitedLiteLlm` acts as a dispatcher that creates fresh, isolated instances for each provider attempt:

```python
class RateLimitedLiteLlm(LiteLlm):
    """Controller that creates isolated LiteLlm instances for each provider"""
    
    # Nested class for Kimi-specific parsing
    class KimiLiteLlm(LiteLlm):
        def _convert_to_llm_response(self, response):
            # Custom Kimi parsing logic built-in
            pass
    
    async def generate_content_async(self, llm_request, stream=False):
        # Create brand new instances for each provider attempt
        async def execute_with_new_llm_instance(model, api_key, api_base, is_kimi):
            if is_kimi:
                # Use specialized Kimi parser
                instance = self.KimiLiteLlm(model=model, api_key=api_key, api_base=api_base)
            else:
                # Use standard LiteLlm for Gemini
                instance = LiteLlm(model=model, api_key=api_key, api_base=api_base)
            
            async for chunk in instance.generate_content_async(llm_request, stream):
                yield chunk
        
        # Try providers in sequence with complete isolation
        try:
            # Novita Kimi
            async for resp in execute_with_new_llm_instance(
                model=kimi_model, api_key=novita_key, api_base=novita_base, is_kimi=True
            ):
                yield resp
            return
        except Exception:
            # DeepInfra Kimi
            async for resp in execute_with_new_llm_instance(
                model=deepinfra_model, api_key=deepinfra_key, api_base=deepinfra_base, is_kimi=True
            ):
                yield resp
            return
        except Exception:
            # Gemini (clean instance, no state pollution)
            async for resp in execute_with_new_llm_instance(
                model=gemini_model, api_key=google_key, api_base=None, is_kimi=False
            ):
                yield resp
```

### Key Architecture Benefits

1. **Complete Isolation**: Each provider attempt uses a brand new `LiteLlm` instance
2. **No State Pollution**: No shared state between provider attempts
3. **Proper Parsing**: Kimi responses get custom parsing, Gemini uses standard parsing
4. **Robust Fallbacks**: Failed attempts don't affect subsequent provider calls
5. **ADK Compatibility**: Maintains proper method signatures and response types

### Configuration Requirements

The system requires proper provider prefixes in `config.py`:

```python
# OpenAI-compatible providers (Novita, DeepInfra)
KIMI_MODEL = "openai/moonshotai/kimi-k2-instruct"
DEEPINFRA_KIMI_MODEL = "openai/moonshotai/Kimi-K2-Instruct"

# Vertex AI provider (Gemini)
FALLBACK_MODEL = "vertex_ai/gemini-1.5-flash-latest"
```

### Testing & Verification

The fix includes comprehensive testing:

```bash
# Run state pollution fix verification
python test_state_pollution_fix.py
```

This test verifies:
- ✅ Isolated LiteLlm instances are created correctly
- ✅ Kimi parsing methods work properly
- ✅ Provider configurations are correct
- ✅ No API base leakage between providers

### Prevention Guidelines

**❌ Don't Do This:**
```python
# Reusing the same LiteLlm instance
model = LiteLlm(model="provider1/model")
model.api_base = "https://provider2.com"  # State pollution!
```

**✅ Do This Instead:**
```python
# Create new instances for each provider
model1 = LiteLlm(model="provider1/model", api_base="https://provider1.com")
model2 = LiteLlm(model="provider2/model", api_base="https://provider2.com")
```

**Key Principles:**
1. **Always create fresh instances** for different API providers
2. **Never modify api_base/api_key** on existing instances
3. **Use provider prefixes** to let LiteLLM handle routing correctly
4. **Implement parsing logic** in subclasses, not monkey-patching
5. **Test isolation** thoroughly with multiple providers

---

## 🔧 Critical Fix: Agent Response Quality and Correctness

### Issues Identified and Resolved

After resolving the state pollution issues, we identified five critical quality problems in the agent's responses:

#### 1. **Tool Call Text Leaking to Frontend**
**Problem**: Users saw debug text like "Let me search for programs..." before actual results  
**Root Cause**: Kimi's "thinking" text was not being filtered out completely during tool call parsing  
**Solution**: Enhanced `KimiLiteLlm._convert_to_llm_response()` with aggressive filtering:

```python
# Heuristic: If we see tool call markers, IGNORE all text content
if "<|tool_calls_section|>" in raw_content or message_container.tool_calls:
    # Only process tool_calls, completely ignore text content
    # This prevents "Let me search..." from leaking through
```

#### 2. **Dead/Incorrect Registration Links**
**Problem**: Agent generated generic URLs instead of using actual registration links  
**Root Cause**: Prompt didn't explicitly require extraction of `activity_url` from tool responses  
**Solution**: Updated `UltraOptimizedAgent` prompt with explicit field extraction:

```python
"**Extract Specific Fields:** For each activity, you MUST use the exact values from these fields:
- `activity_url`: **THE MOST IMPORTANT FIELD. You MUST use this specific URL for the registration link for EACH activity.**
**CRITICAL RULES:**
- **NEVER make up a generic registration link.** Always use the `activity_url` from the data."
```

#### 3. **Missing Activity Dates**
**Problem**: Responses didn't include when activities actually run  
**Root Cause**: Agent wasn't instructed to extract date information from tool responses  
**Solution**: Added mandatory date extraction requirements:

```python
"- `start_date`, `end_date`: The start and end dates of the session.
**CRITICAL RULES:**
- **ALWAYS include the date information** (`start_date`, `end_date`)."
```

#### 4. **Repeated Response on Frontend**
**Problem**: Users saw complete responses twice - once streamed, once as final message  
**Root Cause**: WebSocket endpoint sent incremental chunks AND complete final text  
**Solution**: Implemented delta tracking in `adk_server.py`:

```python
last_partial_text = ""  # Track what has been sent

# Only send new content (delta)
if current_text.startswith(last_partial_text):
    new_chunk = current_text[len(last_partial_text):]
else:
    new_chunk = current_text

if new_chunk:
    await websocket.send_json({"type": "text_chunk", "content": new_chunk})
    last_partial_text = current_text
```

#### 5. **Inconsistent Activity Information**
**Problem**: Activities showed generic information instead of specific details  
**Root Cause**: Agent wasn't following structured data extraction from tool responses  
**Solution**: Added detailed output format requirements:

```python
"**Activity Block Format:** For each activity, format it EXACTLY like this:
**[Activity Name]** ([Display Age])
- **Location:** [Facility]
- **Schedule:** Every [Days of Week] from [Start Time] - [End Time], running from [Start Date] to [End Date].
- **Price:** $[Price]
- **Register Here:** [Activity URL]"
```

### Implementation Details

**Files Modified:**
- `multi_tool_agent/orchestrator.py`: Enhanced UltraOptimizedAgent prompt with explicit data extraction requirements
- `multi_tool_agent/models/rate_limited_llm.py`: Improved Kimi response parsing to prevent text leakage
- `adk_server.py`: Fixed WebSocket streaming to prevent duplicate responses

**Key Architecture Improvements:**
1. **Explicit Data Extraction**: Agent now has detailed instructions for extracting specific fields from tool responses
2. **Aggressive Content Filtering**: Kimi parsing completely ignores text when tool calls are present
3. **Delta-Based Streaming**: WebSocket only sends new content chunks, preventing duplication
4. **Structured Output Requirements**: Mandated format ensures consistent, professional responses

### Testing & Verification

The fixes were verified to resolve:
- ✅ **No tool call text visible** - Clean responses without debug messages
- ✅ **Correct registration links** - Each activity shows its specific `activity_url`
- ✅ **Proper dates displayed** - All activities include start/end dates
- ✅ **No repeated responses** - Smooth streaming without duplication
- ✅ **Accurate activity information** - Complete details from database

### Prevention Guidelines

**For Response Quality:**
1. **Always specify explicit field extraction** in agent prompts
2. **Use structured output formats** with detailed examples
3. **Test with actual tool responses** to verify data extraction
4. **Include mandatory field requirements** in prompts

**For Streaming Responses:**
1. **Track what has been sent** to prevent duplication
2. **Send only deltas** in streaming scenarios
3. **Handle final responses carefully** to avoid re-sending complete text
4. **Test streaming with realistic content** to verify behavior

**For Content Filtering:**
1. **Use heuristics to detect tool calls** and ignore text content
2. **Apply aggressive filtering** when tool calls are present
3. **Test with actual model responses** to verify filtering works
4. **Log filtered content** for debugging purposes

---

## 🔧 Recent Improvements

### Hybrid Search Implementation (Latest)
- **Dense + Sparse Vectors**: Combines semantic understanding with keyword matching
- **Named Vector Search**: Explicitly specifies vector type ("dense") in query_points calls
- **Server-Side Embeddings**: Uses Qdrant's server-side embedding generation for faster performance
- **Fusion Queries**: Uses Qdrant's fusion algorithm for optimal result ranking
- **Database-Level Filtering**: All filters applied at Qdrant level for maximum performance
- **Skip Summarization**: Direct result formatting eliminates expensive LLM calls

### Back-to-Back Class Search
- **Temporal Pairing**: Finds consecutive classes at the same facility
- **Smart Gap Detection**: Configurable time gap limits (default 30 minutes)
- **Common Day Matching**: Ensures classes share at least one day of the week
- **Structured Output**: Clear presentation of paired activities

### System Simplification
- **Archived Legacy Code**: Moved unused files to `archive/` directory
- **Streamlined Architecture**: Single orchestrator + hybrid search tool
- **Placeholder Subagents**: Maintains compatibility while removing complexity
- **Clean Dependencies**: Only essential imports and tools

---

## 📊 Performance Metrics

- **Search Speed**: Sub-second response times
- **Data Coverage**: 10,000+ activities from multiple cities
- **Search Accuracy**: Hybrid approach combines semantic and keyword matching
- **Filter Performance**: Database-level filtering with payload indexes
- **Memory Usage**: Optimized vector storage and retrieval

---

## 🗄️ Archive

The `archive/` directory contains files from the development process that are no longer needed:
- Development documentation and plans
- Previous agent implementations
- Old data processing scripts
- Unused tools and subagents
- Scraping and pipeline scripts (completed)

See `archive/README.md` for detailed documentation of archived files.

---

## 🔍 Troubleshooting

### Common Issues

1. **Qdrant 400 Bad Request Error**
   - **Cause**: Not specifying which vector to use in multi-vector collections
   - **Solution**: Use `models.NamedVector` with `name="dense"` parameter in query_points calls
   - **Example**:
     ```python
     query=models.NamedVector(
         name="dense",  # Specify which vector to use
         vector=models.Document(
             text=query,
             model=AgentConfig.EMBEDDING_MODEL
         )
     )
     ```

2. **Search Fallback to Simple Mode**
   - **Cause**: Advanced search failing due to missing vector specification
   - **Impact**: Degrades to simple scroll-based search without semantic understanding
   - **Solution**: Ensure proper vector configuration in query_points calls

3. **Collection Setup Issues**
   - **Cause**: Mismatched vector names between collection setup and query code
   - **Solution**: Verify vector names match in both `setup_qdrant_collection.py` and search tools

---

## 🚀 Deployment

The system can be deployed to Google Cloud Run using the provided deployment scripts:
- `deploy_cloud_run_adk.sh` - Automated deployment script
- `Dockerfile` - Container configuration
- `deployment.yaml` - Kubernetes deployment (if needed)

The deployment process automatically handles environment variables, dependency installation, and service configuration.