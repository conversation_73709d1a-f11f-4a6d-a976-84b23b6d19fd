import pytest
import asyncio
import sys
from pathlib import Path

# Add the parent directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from unittest.mock import Mock, AsyncMock, patch
from multi_tool_agent.tools.activity_search_tools import _get_qdrant_collection_info, _create_qdrant_filter
from qdrant_client import models, AsyncQdrantClient

class TestQdrantIndexAwareness:
    """Test cases for Qdrant index aware filtering and fallback"""

    @pytest.mark.asyncio
    async def test_qdrant_index_info_caching(self):
        """Test that index info is fetched and cached successfully"""
        client = AsyncMock(AsyncQdrantClient)
        client.get_collection.return_value = AsyncMock(payload_schema={"city": {}, "name": {}})
        
        # Fetch index info
        info = await _get_qdrant_collection_info(client)
        
        # Validate cached info
        assert "indexed_fields" in info
        assert "city" in info["indexed_fields"]
        assert "name" in info["indexed_fields"]
        
        # Call again and ensure it's cached (client method should not be called again)
        client.get_collection.reset_mock()
        cached_info = await _get_qdrant_collection_info(client)
        assert client.get_collection.call_count == 0

    def test_create_qdrant_filter_with_index_awareness(self):
        """Test that filter creation respects the indexed fields"""
        query = "test query"
        filters = {"location": "Burnaby", "max_price": 50, "date": "2023-09-01"}
        indexed_fields = {"city", "price_numeric"}  # Only these fields are indexed

        # Create index-aware filter
        qdrant_filter = _create_qdrant_filter(query, filters, indexed_fields)

        # Should have must conditions
        conditions = qdrant_filter.must if qdrant_filter.must else []
        
        # Check if filters were applied correctly based on indexed fields
        # City filter should be included as 'city' is in indexed_fields
        city_filter = any(cond.key == "city" for cond in conditions)
        assert city_filter, "City filter should be included when city field is indexed"

        # Price filter should be included as 'price_numeric' is in indexed_fields
        price_filter = any(cond.key == "price_numeric" for cond in conditions)
        assert price_filter, "Price filter should be included when price_numeric field is indexed"

        # Date filter should not be in must conditions since date fields are not indexed
        date_in_must = any(cond.key in {"start_date", "end_date"} for cond in conditions)
        assert not date_in_must, "Date filters should not be in must conditions when date fields are not indexed"

