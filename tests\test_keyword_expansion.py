import pytest
from multi_tool_agent.utils.keyword_expansion import expand_keywords, expand_query_text

class TestKeywordExpansion:
    """Test cases for keyword expansion functionality"""
    
    @pytest.mark.parametrize("keywords,expected_expanded", [
        # Dance-related expansions
        (["dancing"], ["ballet", "contemporary", "creative movement", "dance", "dance class", "dance classes", "dancing", "hip hop", "hip-hop", "jazz", "tap"]),
        (["dance"], ["ballet", "contemporary", "creative movement", "dance", "dance class", "dance classes", "dancing", "hip hop", "hip-hop", "jazz", "tap"]),
        (["ballet"], ["ballet", "dance", "dance class", "dancing"]),
        
        # Swimming-related expansions
        (["swimming"], ["aquatics", "pool", "swim", "swim class", "swim lessons", "swimming", "water safety"]),
        (["swim"], ["aquatics", "pool", "swim", "swim class", "swim lessons", "swimming", "water safety"]),
        
        # Art-related expansions
        (["art"], ["art", "arts", "ceramics", "crafts", "creative arts", "drawing", "painting", "pottery", "visual arts"]),
        (["painting"], ["art", "arts", "creative arts", "painting", "visual arts"]),
        
        # Sports-related expansions
        (["sports"], ["athletics", "basketball", "hockey", "skating", "soccer", "sports", "team sports"]),
        (["soccer"], ["football", "futbol", "soccer", "sports", "team sports"]),
        
        # STEM-related expansions
        (["coding"], ["coding", "computer science", "computers", "programming", "robotics", "steam", "stem", "technology"]),
        (["robotics"], ["coding", "robotics", "robots", "steam", "stem", "technology"]),
    ])
    def test_expand_keywords(self, keywords, expected_expanded):
        """Test that keywords are expanded correctly"""
        expanded = expand_keywords(keywords)
        assert expanded == expected_expanded
    
    def test_expand_multiple_keywords(self):
        """Test expansion of multiple keywords"""
        keywords = ["dancing", "swimming"]
        expanded = expand_keywords(keywords)
        
        # Should include all unique terms from both expansions
        assert "dance" in expanded
        assert "ballet" in expanded
        assert "swimming" in expanded
        assert "aquatics" in expanded
        assert len(expanded) > len(keywords)
    
    def test_expand_unknown_keyword(self):
        """Test that unknown keywords are still included in output"""
        keywords = ["unknown_activity"]
        expanded = expand_keywords(keywords)
        assert "unknown_activity" in expanded
        assert len(expanded) == 1
    
    def test_case_normalization(self):
        """Test that keywords are normalized to lowercase"""
        keywords = ["DANCING", "Swimming"]
        expanded = expand_keywords(keywords)
        assert "dancing" in expanded
        assert "swimming" in expanded
        assert "DANCING" not in expanded
        assert "Swimming" not in expanded
    
    def test_expand_query_text(self):
        """Test full query text expansion"""
        query = "Looking for dancing classes"
        expanded = expand_query_text(query)
        
        # Should include original and expanded terms
        assert "dancing" in expanded.lower()
        assert "dance" in expanded.lower()
        # Should not duplicate already present terms
        assert expanded.lower().count("dancing") == 1
    
    def test_no_duplicate_expansion(self):
        """Test that expansion doesn't create duplicates"""
        keywords = ["dance", "dancing", "dance"]
        expanded = expand_keywords(keywords)
        
        # Count occurrences of each term
        term_counts = {}
        for term in expanded:
            term_counts[term] = term_counts.get(term, 0) + 1
        
        # Each term should appear only once
        for count in term_counts.values():
            assert count == 1
