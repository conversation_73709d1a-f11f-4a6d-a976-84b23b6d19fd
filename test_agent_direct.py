#!/usr/bin/env python3
"""
Direct test of the agent to see if it's executing properly
"""
import asyncio
import logging
from google.genai.types import Part, Content
from google.adk.runners import Runner
from google.adk.agents.run_config import RunConfig, StreamingMode
from google.adk.sessions.in_memory_session_service import InMemorySessionService

from multi_tool_agent.orchestrator import root_agent
from multi_tool_agent.memory.graphiti_api_memory_service import GraphitiApiMemoryService

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_agent():
    """Test the agent directly"""
    try:
        # Initialize services
        session_service = InMemorySessionService()
        memory_service = GraphitiApiMemoryService(base_url="http://localhost:8000")
        
        # Create runner
        runner = Runner(
            session_service=session_service,
            memory_service=memory_service,
            app_name="test_app",
            agent=root_agent
        )
        
        # Create session
        session = await session_service.create_session(app_name="test_app", user_id="test_user")
        logger.info(f"Created session: {session.id}")
        
        # Create a test message
        test_message = Content(
            parts=[Part(text="What activities are available today?")],
            role="user"
        )
        
        # Run the agent
        logger.info("Starting agent execution...")
        event_count = 0
        async for event in runner.run_async(
            new_message=test_message,
            user_id="test_user",
            session_id=session.id,
            run_config=RunConfig(streaming_mode=StreamingMode.SSE)
        ):
            event_count += 1
            logger.info(f"Event #{event_count}:")
            
            # Check for text content
            if hasattr(event, 'content') and event.content and hasattr(event.content, 'parts'):
                for part in event.content.parts:
                    if hasattr(part, 'text') and part.text:
                        logger.info(f"  Text: {part.text[:200]}...")
            
            # Check for function calls
            function_calls = event.get_function_calls()
            if function_calls:
                for fc in function_calls:
                    logger.info(f"  Function Call: {fc.name} with args: {fc.args}")
            
            # Check for function responses
            function_responses = event.get_function_responses()
            if function_responses:
                for fr in function_responses:
                    logger.info(f"  Function Response: {fr.name} -> {fr.response}")
            
            # Check if turn is complete
            if hasattr(event, 'turn_complete') and event.turn_complete:
                logger.info("  TURN COMPLETE")
                break
        
        logger.info(f"Total events received: {event_count}")
        
    except Exception as e:
        logger.error(f"Error during test: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(test_agent())
