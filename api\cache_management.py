# api/cache_management.py
"""
Cache management utilities for Gemini Explicit Caching.
Provides admin endpoints for monitoring and managing cache objects.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from fastapi import HTTPException

logger = logging.getLogger(__name__)

async def list_active_caches() -> Dict[str, Any]:
    """
    List all active Gemini Cache objects.
    
    Returns:
        Dictionary containing list of active caches with their details
    """
    try:
        # Import the google.generativeai caching module
        try:
            from google.generativeai import caching
        except ImportError:
            raise HTTPException(
                status_code=503, 
                detail="google.generativeai not available for cache management"
            )
        
        # List all active caches
        caches = []
        async for cache in caching.CachedContent.list_async():
            cache_info = {
                "name": cache.name,
                "display_name": getattr(cache, 'display_name', None),
                "model": cache.model,
                "create_time": cache.create_time.isoformat() if cache.create_time else None,
                "update_time": cache.update_time.isoformat() if cache.update_time else None,
                "expire_time": cache.expire_time.isoformat() if cache.expire_time else None,
                "usage_metadata": getattr(cache, 'usage_metadata', None),
            }
            caches.append(cache_info)
        
        return {
            "active_caches": caches,
            "total_count": len(caches),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to list active caches: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list active caches: {str(e)}"
        )

async def get_cache_details(cache_name: str) -> Dict[str, Any]:
    """
    Get detailed information about a specific cache.
    
    Args:
        cache_name: Name of the cache to retrieve details for
        
    Returns:
        Dictionary containing detailed cache information
    """
    try:
        # Import the google.generativeai caching module
        try:
            from google.generativeai import caching
        except ImportError:
            raise HTTPException(
                status_code=503, 
                detail="google.generativeai not available for cache management"
            )
        
        # Get specific cache details
        cache = await caching.CachedContent.get_async(cache_name)
        
        cache_details = {
            "name": cache.name,
            "display_name": getattr(cache, 'display_name', None),
            "model": cache.model,
            "create_time": cache.create_time.isoformat() if cache.create_time else None,
            "update_time": cache.update_time.isoformat() if cache.update_time else None,
            "expire_time": cache.expire_time.isoformat() if cache.expire_time else None,
            "usage_metadata": getattr(cache, 'usage_metadata', None),
            "system_instruction": getattr(cache, 'system_instruction', None),
            "contents": getattr(cache, 'contents', []),
            "tool_config": getattr(cache, 'tool_config', None),
        }
        
        return cache_details
        
    except Exception as e:
        logger.error(f"Failed to get cache details for {cache_name}: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get cache details: {str(e)}"
        )

async def delete_cache(cache_name: str) -> Dict[str, Any]:
    """
    Delete a specific cache.
    
    Args:
        cache_name: Name of the cache to delete
        
    Returns:
        Dictionary containing deletion result
    """
    try:
        # Import the google.generativeai caching module
        try:
            from google.generativeai import caching
        except ImportError:
            raise HTTPException(
                status_code=503, 
                detail="google.generativeai not available for cache management"
            )
        
        # Delete the cache
        await caching.CachedContent.delete_async(cache_name)
        
        return {
            "status": "success",
            "message": f"Cache {cache_name} deleted successfully",
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to delete cache {cache_name}: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete cache: {str(e)}"
        )

async def get_cache_usage_stats() -> Dict[str, Any]:
    """
    Get usage statistics for all active caches.
    
    Returns:
        Dictionary containing cache usage statistics
    """
    try:
        # Import the google.generativeai caching module
        try:
            from google.generativeai import caching
        except ImportError:
            raise HTTPException(
                status_code=503, 
                detail="google.generativeai not available for cache management"
            )
        
        # Collect usage statistics
        total_caches = 0
        total_cached_tokens = 0
        models_used = set()
        oldest_cache = None
        newest_cache = None
        
        async for cache in caching.CachedContent.list_async():
            total_caches += 1
            models_used.add(cache.model)
            
            # Track oldest and newest caches
            if cache.create_time:
                if oldest_cache is None or cache.create_time < oldest_cache:
                    oldest_cache = cache.create_time
                if newest_cache is None or cache.create_time > newest_cache:
                    newest_cache = cache.create_time
            
            # Sum cached tokens if available
            if hasattr(cache, 'usage_metadata') and cache.usage_metadata:
                if hasattr(cache.usage_metadata, 'total_token_count'):
                    total_cached_tokens += cache.usage_metadata.total_token_count
        
        return {
            "total_active_caches": total_caches,
            "total_cached_tokens": total_cached_tokens,
            "models_used": list(models_used),
            "oldest_cache_created": oldest_cache.isoformat() if oldest_cache else None,
            "newest_cache_created": newest_cache.isoformat() if newest_cache else None,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get cache usage stats: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get cache usage stats: {str(e)}"
        )

def get_session_cache_mappings() -> Dict[str, Any]:
    """
    Get current session to cache mappings from the RateLimitedLiteLlm class.
    
    Returns:
        Dictionary containing session cache mappings
    """
    try:
        # Import the cache mappings from the rate_limited_llm module
        from multi_tool_agent.models.rate_limited_llm import _session_cache_map, _cache_creation_times
        
        # Convert to serializable format
        mappings = {}
        for session_id, cache_name in _session_cache_map.items():
            creation_time = _cache_creation_times.get(session_id)
            mappings[session_id] = {
                "cache_name": cache_name,
                "creation_time": datetime.fromtimestamp(creation_time).isoformat() if creation_time else None
            }
        
        return {
            "session_mappings": mappings,
            "total_sessions": len(mappings),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get session cache mappings: {e}", exc_info=True)
        return {
            "error": str(e),
            "session_mappings": {},
            "total_sessions": 0,
            "timestamp": datetime.now().isoformat()
        }
