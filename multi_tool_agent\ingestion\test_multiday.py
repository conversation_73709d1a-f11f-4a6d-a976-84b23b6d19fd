#!/usr/bin/env python3
import json

# Load the data
with open('fast_dropin_activities.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print(f"Total activities: {len(data)}")

# Find multi-day activities (those with multiple weekdays_mask values)
multi_day = [act for act in data if act.get('weekdays_mask') and len(act.get('weekdays_mask', [])) > 1]

print(f"Multi-day activities: {len(multi_day)}")

print("\nFirst 3 multi-day activities:")
for i, act in enumerate(multi_day[:3]):
    print(f"{i+1}. {act['name']}")
    print(f"   weekdays_mask: {act['weekdays_mask']}")
    print(f"   days_of_week_list: {act['days_of_week_list']}")
    print(f"   days_of_week: {act.get('days_of_week', 'N/A')}")
    print()

# Check the specific example from the user
swimfit_activities = [act for act in data if 'Swimfit' in act['name']]
print(f"Swimfit activities found: {len(swimfit_activities)}")
for act in swimfit_activities[:2]:
    print(f"- {act['name']}")
    print(f"  weekdays_mask: {act['weekdays_mask']}")
    print(f"  days_of_week_list: {act['days_of_week_list']}")
    print(f"  days_of_week: {act.get('days_of_week', 'N/A')}")
    print()
