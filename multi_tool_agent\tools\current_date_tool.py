# multi_tool_agent/tools/current_date_tool.py
from datetime import datetime, timedelta
from google.adk.tools import BaseTool, ToolContext
from google.genai.types import FunctionDeclaration, Schema, Type


class GetCurrentDate(BaseTool):
    """Tool to get the current date in YYYY-MM-DD format"""
    
    def __init__(self):
        super().__init__(
            name="get_current_date",
            description=(
                "Returns the current date in YYYY-MM-DD format. "
                "Call this tool to resolve relative dates like 'today' or 'tomorrow' "
                "before calling other tools that require a specific date."
            )
        )

    def _get_declaration(self) -> FunctionDeclaration:
        return FunctionDeclaration(
            name=self.name,
            description=self.description,
            parameters=Schema(
                type=Type.OBJECT,
                properties={},
                required=[],
            ),
        )

    async def run_async(self, *, args: dict, tool_context: ToolContext) -> str:
        """Returns the current date in YYYY-MM-DD format"""
        current_date = datetime.now().strftime('%Y-%m-%d')
        return f"Today's date is {current_date}"


class GetWeekendDates(BaseTool):
    """Tool to get the upcoming weekend dates"""
    
    def __init__(self):
        super().__init__(
            name="get_weekend_dates",
            description=(
                "Returns the dates for the upcoming Saturday and Sunday in YYYY-MM-DD format. "
                "Call this tool when the user asks for activities 'this weekend'."
            )
        )

    def _get_declaration(self) -> FunctionDeclaration:
        return FunctionDeclaration(
            name=self.name,
            description=self.description,
            parameters=Schema(
                type=Type.OBJECT,
                properties={},
                required=[],
            ),
        )

    async def run_async(self, *, args: dict, tool_context: ToolContext) -> str:
        """Returns the dates for the upcoming weekend"""
        today = datetime.now()
        
        # Calculate days until Saturday (5 = Saturday, 6 = Sunday in weekday())
        # If today is Saturday (5), get this Saturday. If Sunday (6), get next Saturday.
        days_until_saturday = (5 - today.weekday()) % 7
        if days_until_saturday == 0 and today.weekday() == 6:  # If today is Sunday
            days_until_saturday = 6  # Get next Saturday
        elif days_until_saturday == 0:  # If today is Saturday
            days_until_saturday = 0  # Use today
            
        saturday = today + timedelta(days=days_until_saturday)
        sunday = saturday + timedelta(days=1)
        
        return f"This weekend dates: Saturday {saturday.strftime('%Y-%m-%d')}, Sunday {sunday.strftime('%Y-%m-%d')}"


# For backward compatibility, also provide simple functions
def get_current_date() -> str:
    """Simple function version - returns current date in YYYY-MM-DD format"""
    return datetime.now().strftime('%Y-%m-%d')


def get_weekend_dates() -> dict:
    """Simple function version - returns weekend dates"""
    today = datetime.now()
    days_until_saturday = (5 - today.weekday()) % 7
    if days_until_saturday == 0 and today.weekday() == 6:  # If today is Sunday
        days_until_saturday = 6
    elif days_until_saturday == 0:  # If today is Saturday
        days_until_saturday = 0
        
    saturday = today + timedelta(days=days_until_saturday)
    sunday = saturday + timedelta(days=1)
    
    return {
        "saturday": saturday.strftime('%Y-%m-%d'),
        "sunday": sunday.strftime('%Y-%m-%d')
    }
