# multi_tool_agent/tools/location_tools.py

import logging
from typing import Dict, Optional

logger = logging.getLogger(__name__)

# In a real-world application, this would call an API like Google Maps Directions.
# For now, we use a mock "distance matrix" for key locations.
MOCK_TRAVEL_TIMES_MINUTES: Dict[str, Dict[str, int]] = {
    "təməsew̓txʷ Aquatic and Community Centre": {
        "Canada Games Pool": 8,
        "Century House": 10,
        "Queensborough Community Centre": 15,
        "Anvil Centre": 12,
        "Moody Park Arena": 7
    },
    "Canada Games Pool": {
        "təməsew̓txʷ Aquatic and Community Centre": 8,
        "Century House": 5,
        "Queensborough Community Centre": 12,
        "Anvil Centre": 9,
        "Moody Park Arena": 4
    },
    "Century House": {
        "təməsew̓txʷ Aquatic and Community Centre": 10,
        "Canada Games Pool": 5,
        "Queensborough Community Centre": 18,
        "Anvil Centre": 11,
        "Moody Park Arena": 2
    },
    "Queensborough Community Centre": {
        "təməsew̓txʷ Aquatic and Community Centre": 15,
        "Canada Games Pool": 12,
        "Century House": 18,
        "Anvil Centre": 20,
        "Moody Park Arena": 16
    },
    "Anvil Centre": {
        "təməsew̓txʷ Aquatic and Community Centre": 12,
        "Canada Games Pool": 9,
        "Century House": 11,
        "Queensborough Community Centre": 20,
        "Moody Park Arena": 10
    },
     "Moody Park Arena": {
        "təməsew̓txʷ Aquatic and Community Centre": 7,
        "Canada Games Pool": 4,
        "Century House": 2,
        "Queensborough Community Centre": 16,
        "Anvil Centre": 10
    }
}

def get_travel_time(origin_facility: str, destination_facility: str) -> Optional[int]:
    """
    Estimates the travel time in minutes between two facilities.

    Args:
        origin_facility: The starting facility name.
        destination_facility: The destination facility name.

    Returns:
        The estimated travel time in minutes, or None if the route is unknown.
    """
    if not origin_facility or not destination_facility:
        return None
    logger.info(f"Calculating travel time from '{origin_facility}' to '{destination_facility}'")
    if origin_facility == destination_facility:
        return 0
    
    try:
        # Check both directions in the matrix
        time = MOCK_TRAVEL_TIMES_MINUTES.get(origin_facility, {}).get(destination_facility)
        if time is None:
            time = MOCK_TRAVEL_TIMES_MINUTES.get(destination_facility, {}).get(origin_facility)
        return time
    except Exception:
        logger.warning(f"Could not calculate travel time between {origin_facility} and {destination_facility}")
        return None
