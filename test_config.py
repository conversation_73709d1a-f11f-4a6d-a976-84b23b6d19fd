#!/usr/bin/env python3
"""
Simple configuration test to verify abundant capacity settings.
"""

import sys
import os

# Add the multi_tool_agent directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'multi_tool_agent'))

try:
    from multi_tool_agent.config import AgentConfig
    from multi_tool_agent.orchestrator import root_agent
    
    print('🚀 ABUNDANT CAPACITY CONFIGURATION TEST')
    print('=' * 50)
    print(f'📊 ENABLE_API_OPTIMIZATION: {AgentConfig.ENABLE_API_OPTIMIZATION}')
    print(f'📊 MAX_API_CALLS_PER_REQUEST: {AgentConfig.MAX_API_CALLS_PER_REQUEST}')
    print(f'📊 ULTRA_LOW_API_MODE: {AgentConfig.ULTRA_LOW_API_MODE}')
    print(f'📊 KIMI_MIN_INTERVAL_SECONDS: {AgentConfig.KIMI_MIN_INTERVAL_SECONDS}')
    print(f'📊 ENABLE_CONVERSATION: {AgentConfig.ENABLE_CONVERSATION}')
    print(f'📊 DEEPINFRA_CONCURRENT_LIMIT: {getattr(AgentConfig, "DEEPINFRA_CONCURRENT_LIMIT", "Not set")}')
    print(f'📊 DEEPINFRA_EFFECTIVE_RPM: {getattr(AgentConfig, "DEEPINFRA_EFFECTIVE_RPM", "Not set")}')
    print()
    print(f'🤖 Selected Agent: {root_agent.name}')
    print(f'📋 Agent Description: {root_agent.description[:100]}...')
    print()
    
    # Test rate limiter configuration
    try:
        from multi_tool_agent.utils.rate_limiter import get_hybrid_manager
        manager = get_hybrid_manager()
        print('🔧 RATE LIMITER TEST:')
        print(f'   Kimi Model: {manager.kimi_model}')
        print(f'   DeepInfra Model: {manager.deepinfra_kimi_model}')
        print(f'   Use DeepInfra: {manager.use_deepinfra}')
        print(f'   Fallback Model: {manager.fallback_model}')
        print()
    except Exception as e:
        print(f'⚠️ Rate limiter test failed: {e}')
        print()
    
    # Verify configuration changes
    print('✅ CONFIGURATION VERIFICATION:')
    
    if not AgentConfig.ENABLE_API_OPTIMIZATION:
        print('✅ API optimization disabled - optimizing for UX')
    else:
        print('⚠️ API optimization still enabled')
    
    if AgentConfig.MAX_API_CALLS_PER_REQUEST >= 10:
        print('✅ High API call limit - rich dialogue enabled')
    else:
        print(f'⚠️ Low API call limit: {AgentConfig.MAX_API_CALLS_PER_REQUEST}')
    
    if not AgentConfig.ULTRA_LOW_API_MODE:
        print('✅ Ultra-low API mode disabled - abundant capacity')
    else:
        print('⚠️ Ultra-low API mode still enabled')
    
    if AgentConfig.ENABLE_CONVERSATION:
        print('✅ Conversation mode enabled')
    else:
        print('⚠️ Conversation mode disabled')
    
    if root_agent.name == "DialogueAgent":
        print('✅ DialogueAgent selected - rich dialogue ready')
    else:
        print(f'⚠️ Different agent selected: {root_agent.name}')
    
    print()
    print('🎯 CAPACITY SUMMARY:')
    print('   Novita: 10 RPM (primary)')
    print('   DeepInfra: 200 concurrent requests (~4000 RPM effective)')
    print('   Total: Essentially unlimited for dialogue')
    print()
    print('✅ Configuration successfully updated for abundant capacity!')
    
except ImportError as e:
    print(f'❌ Import error: {e}')
    print('Make sure you are running from the correct directory')
except Exception as e:
    print(f'❌ Configuration test failed: {e}')
    import traceback
    traceback.print_exc()
