"""
Test script for the new DialogueAgent multi-turn conversation capabilities.
"""

import asyncio
import logging
from google.adk.session import InMemorySessionService
from google.adk.memory import GraphitiApiMemoryService
from google.adk.runner import Runner
from google.genai import types
from multi_tool_agent.orchestrator import root_agent

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_dialogue_flow():
    """Test the multi-turn dialogue capabilities."""
    
    # --- Session Management ---
    session_service = InMemorySessionService()
    APP_NAME, USER_ID, SESSION_ID = "dialogue_test_app", "test_user_dialogue", "dialogue_session_01"
    
    await session_service.create_session(
        app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID, state={}
    )
    print(f"✅ Session created: App='{APP_NAME}', User='{USER_ID}', Session='{SESSION_ID}'")
    
    # --- Memory Service ---
    try:
        memory_service = GraphitiApiMemoryService(base_url="http://localhost:8001")
        print("✅ Memory service connected")
    except Exception as e:
        print(f"⚠️ Memory service not available: {e}")
        memory_service = None
    
    # --- Runner ---
    runner = Runner(
        agent=root_agent,
        app_name=APP_NAME,
        session_service=session_service,
        memory_service=memory_service
    )
    
    print(f"\n🤖 Testing DialogueAgent: {root_agent.name}")
    print("=" * 60)
    
    # --- Test Conversation Flow ---
    conversation_turns = [
        "Hi! I'm looking for activities for my kid",
        "She's 5 years old",
        "We're in Burnaby",
        "Swimming would be great!",
        "What about weekday options?"
    ]
    
    for i, user_input in enumerate(conversation_turns, 1):
        print(f"\n🗣️ Turn {i}: {user_input}")
        print("-" * 40)
        
        content = types.Content(role='user', parts=[types.Part(text=user_input)])
        
        final_response = "No response received"
        
        async for event in runner.run_async(
            user_id=USER_ID,
            session_id=SESSION_ID,
            new_message=content
        ):
            if event.is_final_response():
                if event.content and event.content.parts and event.content.parts[0].text:
                    final_response = event.content.parts[0].text.strip()
                break
        
        print(f"🤖 Agent: {final_response}")
        
        # Add a small delay between turns to simulate natural conversation
        await asyncio.sleep(1)
    
    print("\n" + "=" * 60)
    print("✅ Dialogue test completed!")

async def test_single_turn_vs_dialogue():
    """Compare single-turn vs dialogue responses."""
    
    # --- Session Management ---
    session_service = InMemorySessionService()
    APP_NAME = "comparison_test_app"
    
    # Test queries
    incomplete_query = "Looking for activities for my kid"
    complete_query = "Looking for swimming activities for my 5-year-old in Burnaby on weekdays"
    
    print("\n🔬 COMPARISON TEST: Single-turn vs Dialogue")
    print("=" * 60)
    
    for query_type, query in [("Incomplete", incomplete_query), ("Complete", complete_query)]:
        print(f"\n📝 Testing {query_type} Query: '{query}'")
        print("-" * 50)
        
        # Create new session for each test
        session_id = f"test_session_{query_type.lower()}"
        await session_service.create_session(
            app_name=APP_NAME, user_id="test_user", session_id=session_id, state={}
        )
        
        runner = Runner(
            agent=root_agent,
            app_name=APP_NAME,
            session_service=session_service,
            memory_service=None
        )
        
        content = types.Content(role='user', parts=[types.Part(text=query)])
        
        async for event in runner.run_async(
            user_id="test_user",
            session_id=session_id,
            new_message=content
        ):
            if event.is_final_response():
                if event.content and event.content.parts and event.content.parts[0].text:
                    response = event.content.parts[0].text.strip()
                    print(f"🤖 Response: {response[:200]}...")
                break

if __name__ == "__main__":
    print("🚀 Starting DialogueAgent Tests")
    print(f"📋 Current agent: {root_agent.name}")
    print(f"📋 Agent description: {root_agent.description}")
    
    asyncio.run(test_dialogue_flow())
    asyncio.run(test_single_turn_vs_dialogue())
