# multi_tool_agent/memory/graphiti_api_memory_service.py
import httpx
import logging
from datetime import datetime
from google.adk.memory import BaseMemoryService
from google.adk.memory.base_memory_service import SearchMemoryResponse, MemoryEntry
from google.genai.types import Content, Part

logger = logging.getLogger(__name__)

class GraphitiApiMemoryService(BaseMemoryService):
    """
    ADK Memory Service adapter that communicates with a remote Graphiti FastAPI service.
    """
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        # Increase timeout for potentially slow graph operations
        self.http_client = httpx.AsyncClient(timeout=60.0)
        self.user_id = None # Context for the current user

    def set_user_id(self, user_id: str):
        self.user_id = user_id

    async def add_session_to_memory(self, session) -> None:
        """
        Adds the complete session's conversation to the memory graph.
        This method is called by the ADK Runner when the session is being saved.
        The session contains all the events (user messages + agent responses) and state.
        """
        try:
            user_id = session.user_id
            session_id = session.id
            
            # Process each event in the session that hasn't been stored yet
            # Events include both user messages and agent responses
            for event in session.events:
                # Only process events that have text content
                if not (hasattr(event, 'content') and event.content and 
                       hasattr(event.content, 'parts') and event.content.parts and 
                       event.content.parts[0].text):
                    continue
                    
                # Extract the text content from the event
                event_text = event.content.parts[0].text
                author = getattr(event, 'author', 'unknown')
                timestamp = getattr(event, 'timestamp', datetime.now().timestamp())
                
                # Create episode payload for Graphiti API
                payload = {
                    "episode": {
                        "name": f"Conversation turn from {author}",
                        "content": event_text,
                        "source_description": f"ADK session {session_id} - {author} message",
                        "reference_time": datetime.fromtimestamp(timestamp).isoformat()
                    },
                    "group_id": user_id
                }
                
                # Send to Graphiti API
                try:
                    response = await self.http_client.post(f"{self.base_url}/add_episode", json=payload)
                    response.raise_for_status()
                    logger.info(f"🧠 Added memory for {author}: '{event_text[:60]}...'")
                except httpx.HTTPStatusError as e:
                    logger.error(f"❌ Failed to add session memory for user {user_id}. Status: {e.response.status_code}")
                except Exception as e:
                    logger.error(f"❌ Error adding session memory for user {user_id}: {e}")
                    
        except Exception as e:
            logger.error(f"❌ Failed to process session for memory storage: {e}", exc_info=True)
        
    async def search_memory(self, *, app_name: str, user_id: str, query: str) -> SearchMemoryResponse:
        """Queries the Graphiti API for facts relevant to the user and query."""
        try:
            # The search is scoped to the user's data PLUS public 'bc_local_user' data
            params = {"query": query, "group_ids": [user_id, "bc_local_user"]}
            response = await self.http_client.get(f"{self.base_url}/search", params=params)
            response.raise_for_status()
            data = response.json()
            
            # Create MemoryEntry objects instead of raw strings
            memories = []
            for fact_data in data.get("facts", []):
                fact_text = fact_data.get("fact")
                if fact_text:
                    # Create the required nested structure for the response
                    memories.append(
                        MemoryEntry(
                           content=Content(parts=[Part(text=fact_text)]),
                           author="memory" # This identifies the source as retrieved memory
                        )
                    )
            
            return SearchMemoryResponse(memories=memories)
        except httpx.HTTPError as e:
            logger.error(f"Failed to search memory via API for user {user_id}: {e}")
            return SearchMemoryResponse(memories=[])
        except Exception as e:
            logger.error(f"Error in search_memory: {e}")
            return SearchMemoryResponse(memories=[])
