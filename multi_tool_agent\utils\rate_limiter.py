"""
Rate Limiting Utility for Kimi K2 API (10 RPM limit)
Implements request queuing and fallback model strategies.
"""

import asyncio
import time
import logging
from typing import Dict, Any, Optional, Callable, Awaitable
from dataclasses import dataclass
from collections import deque
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

@dataclass
class RateLimitConfig:
    """Configuration for rate limiting."""
    rpm: int = 10                    # Requests per minute
    min_interval: float = 6.5        # Minimum seconds between requests (with buffer)
    max_queue_size: int = 50         # Maximum queued requests
    queue_timeout: float = 30.0      # Timeout for queued requests
    enable_fallback: bool = True     # Use fallback models when rate limited

class KimiRateLimiter:
    """Rate limiter specifically for Kimi K2 API with configurable RPM limit."""
    
    def __init__(self, config: RateLimitConfig, provider_name: str = "<PERSON><PERSON>"):
        self.config = config
        self.provider_name = provider_name
        self.last_request_time = 0.0
        self.request_queue = deque()
        self.queue_lock = asyncio.Lock()
        self.is_processing = False
        self.request_count = 0
        self.rate_limited_until = 0.0
        
        # Track request times for RPM calculation
        self.request_times = deque(maxlen=config.rpm)
        
        logger.info(f"🎯 Initialized {provider_name} rate limiter (RPM: {config.rpm}, min_interval: {config.min_interval}s)")
        
    async def is_rate_limited(self) -> bool:
        """Check if we're currently rate limited."""
        current_time = time.time()
        
        # Sanity check: if rate_limited_until is more than 1 hour in the future, reset it
        if self.rate_limited_until > current_time + 3600:
            logger.warning(f"🔧 Resetting unreasonable rate limit timestamp: {self.rate_limited_until} (current: {current_time})")
            self.rate_limited_until = 0.0
        
        # Check if we're in a rate limit cooldown period
        if current_time < self.rate_limited_until:
            logger.debug(f"🕒 {self.provider_name} in cooldown until {self.rate_limited_until} (current: {current_time})")
            return True
            
        # Check RPM limit
        if len(self.request_times) >= self.config.rpm:
            oldest_request = self.request_times[0]
            if current_time - oldest_request < 60:  # Within last minute
                logger.debug(f"📦 {self.provider_name} RPM limit reached: {len(self.request_times)} requests in last minute")
                return True
                
        # Check minimum interval
        if self.last_request_time > 0 and current_time - self.last_request_time < self.config.min_interval:
            time_since_last = current_time - self.last_request_time
            logger.debug(f"⏱️ {self.provider_name} min interval not met: {time_since_last:.1f}s < {self.config.min_interval}s")
            return True
            
        return False
    
    async def wait_for_availability(self) -> bool:
        """Wait until we can make a request. Returns False if timeout."""
        start_time = time.time()
        
        while await self.is_rate_limited():
            if time.time() - start_time > self.config.queue_timeout:
                logger.warning("⏰ Rate limiter timeout - request abandoned")
                return False
                
            # Calculate wait time
            current_time = time.time()
            wait_time = max(
                self.config.min_interval - (current_time - self.last_request_time),
                self.rate_limited_until - current_time,
                0.1  # Minimum wait
            )
            
            logger.info(f"⏳ {self.provider_name} rate limited - waiting {wait_time:.1f}s for availability")
            await asyncio.sleep(wait_time)
            
        return True
    
    async def record_request(self):
        """Record that a request was made."""
        current_time = time.time()
        self.last_request_time = current_time
        self.request_times.append(current_time)
        self.request_count += 1
        logger.info(f"📊 {self.provider_name} Kimi request #{self.request_count} recorded at {current_time}")
    
    async def record_rate_limit_error(self, retry_after: Optional[float] = None):
        """Record that we hit a rate limit error."""
        current_time = time.time()
        if retry_after:
            self.rate_limited_until = current_time + retry_after
        else:
            self.rate_limited_until = current_time + self.config.min_interval * 2
            
        logger.warning(f"🚫 {self.provider_name} rate limit hit - cooling down until {self.rate_limited_until}")
    
    @asynccontextmanager
    async def acquire(self):
        """Context manager to acquire rate limit permission."""
        if not await self.wait_for_availability():
            raise TimeoutError("Rate limiter timeout")
            
        try:
            await self.record_request()
            yield
        except Exception as e:
            # Only treat as rate limit error if it's actually a 429 or specific rate limit message
            error_str = str(e).lower()
            if "429" in error_str or "rate limit" in error_str or "too many requests" in error_str:
                await self.record_rate_limit_error()
            raise

class HybridModelManager:
    """Manages hybrid model usage - Kimi for reasoning, fallbacks for tools."""
    
    def __init__(self):
        from multi_tool_agent.config import AgentConfig
        self.kimi_model = AgentConfig.KIMI_MODEL
        self.deepinfra_kimi_model = AgentConfig.DEEPINFRA_KIMI_MODEL
        self.deepinfra_api_base = AgentConfig.DEEPINFRA_API_BASE
        self.deepinfra_api_key = AgentConfig.DEEPINFRA_API_KEY
        self.use_deepinfra = AgentConfig.USE_DEEPINFRA_FALLBACK and self.deepinfra_api_key
        self.fallback_model = AgentConfig.FALLBACK_MODEL
        self.cheap_model = AgentConfig.CHEAP_MODEL
        self.use_hybrid = AgentConfig.USE_HYBRID_MODELS
        self.kimi_for_reasoning_only = AgentConfig.KIMI_FOR_REASONING_ONLY
        
        # Initialize rate limiters for both Kimi providers
        # Novita rate config (10 RPM)
        novita_config = RateLimitConfig(
            rpm=AgentConfig.KIMI_RATE_LIMIT_RPM,
            min_interval=AgentConfig.KIMI_MIN_INTERVAL_SECONDS,
            max_queue_size=AgentConfig.MAX_QUEUE_SIZE,
            queue_timeout=AgentConfig.QUEUE_TIMEOUT_SECONDS,
            enable_fallback=AgentConfig.USE_FALLBACK_ON_RATE_LIMIT
        )
        self.kimi_limiter = KimiRateLimiter(novita_config, provider_name="Novita")
        
        # DeepInfra rate config (60 RPM)
        deepinfra_config = RateLimitConfig(
            rpm=getattr(AgentConfig, 'DEEPINFRA_RATE_LIMIT_RPM', 60),  # 60 RPM for DeepInfra
            min_interval=1.1,  # 60/60 = 1 second, with 0.1s buffer
            max_queue_size=AgentConfig.MAX_QUEUE_SIZE,
            queue_timeout=AgentConfig.QUEUE_TIMEOUT_SECONDS,
            enable_fallback=AgentConfig.USE_FALLBACK_ON_RATE_LIMIT
        )
        self.deepinfra_limiter = KimiRateLimiter(deepinfra_config, provider_name="DeepInfra")
        
    def get_model_for_task(self, task_type: str, is_critical: bool = False, force_gemini: bool = False) -> str:
        """Get the appropriate model for a specific task type - balanced Kimi usage."""
        # Force Gemini if requested (e.g., when rate limited)
        if force_gemini:
            if task_type in ["tool_execution", "simple_response", "search"]:
                return self.cheap_model  # Gemini Flash Lite for simple tasks
            else:
                return self.fallback_model  # Gemini 2.5 Flash for intermediate tasks
                
        if not self.use_hybrid:
            return self.kimi_model

        # Strategic Kimi usage for where it adds most value:
        # 1. Final output generation (better reasoning and formatting)
        # 2. Agent delegation and orchestration (complex decision making)
        # 3. Tool calling decisions (better tool selection)
        # 4. Critical reasoning tasks

        kimi_tasks = [
            "reasoning",           # Complex reasoning
            "orchestration",       # Agent delegation
            "final_output",        # Final response generation
            "tool_calling",        # Tool selection and calling
            "delegation",          # Agent-to-agent delegation
            "response_generation", # Final response formatting
            "complex_analysis"     # Complex analysis tasks
        ]

        if task_type in kimi_tasks or is_critical:
            return self.kimi_model

        # Use unlimited Gemini for simpler tasks
        if task_type in ["tool_execution", "simple_response", "search"]:
            return self.cheap_model  # Gemini Flash Lite for simple tasks
        else:
            return self.fallback_model  # Gemini 2.5 Flash for intermediate tasks
    
    async def should_use_kimi(self, task_type: str, is_critical: bool = False) -> bool:
        """Determine if we should use Kimi for this task - balanced strategy."""
        if not self.use_hybrid:
            return True

        # Strategic Kimi usage for high-value tasks
        kimi_tasks = [
            "reasoning", "orchestration", "final_output", "tool_calling",
            "delegation", "response_generation", "complex_analysis"
        ]

        requires_kimi = task_type in kimi_tasks or is_critical

        if not requires_kimi:
            logger.info(f"⚡ Using unlimited Gemini for {task_type} (critical={is_critical})")
            return False

        # Check rate limiting for Kimi
        if await self.kimi_limiter.is_rate_limited():
            logger.warning(f"🚫 Kimi rate limited - using unlimited Gemini for {task_type}")
            return False

        logger.info(f"🧠 Using Kimi K2 for strategic {task_type} (critical={is_critical})")
        return True
    
    async def execute_with_rate_limiting(self, 
                                       llm_call: Callable[..., Awaitable[Any]], 
                                       task_type: str = "unknown",
                                       is_critical: bool = False,
                                       **kwargs) -> Any:
        """Execute LLM call with rate limiting and fallback logic."""
        
        # Determine if we should use Kimi
        use_kimi = await self.should_use_kimi(task_type, is_critical)
        
        if use_kimi:
            # Try Novita Kimi first
            try:
                async with self.kimi_limiter.acquire():
                    logger.info(f"🧠 Using Novita Kimi K2 for {task_type} (critical={is_critical})")
                    return await llm_call(model=self.kimi_model, **kwargs)
            except (TimeoutError, Exception) as e:
                if "rate" in str(e).lower() or "429" in str(e):
                    logger.warning(f"🚫 Novita Kimi rate limited for {task_type}")
                    await self.kimi_limiter.record_rate_limit_error()
                    
                    # Try DeepInfra Kimi as secondary fallback
                    if self.use_deepinfra:
                        try:
                            async with self.deepinfra_limiter.acquire():
                                logger.info(f"🔄 Using DeepInfra Kimi K2 as fallback for {task_type}")
                                # Pass DeepInfra-specific parameters
                                deepinfra_kwargs = kwargs.copy()
                                deepinfra_kwargs['api_base'] = self.deepinfra_api_base
                                deepinfra_kwargs['api_key'] = self.deepinfra_api_key
                                return await llm_call(model=self.deepinfra_kimi_model, **deepinfra_kwargs)
                        except (TimeoutError, Exception) as deepinfra_e:
                            if "rate" in str(deepinfra_e).lower() or "429" in str(deepinfra_e):
                                logger.warning(f"🚫 DeepInfra Kimi also rate limited for {task_type}")
                                await self.deepinfra_limiter.record_rate_limit_error()
                                # Fall through to Gemini
                            else:
                                logger.error(f"❌ DeepInfra Kimi error: {deepinfra_e}")
                                # Fall through to Gemini
                else:
                    raise
        
        # Use Gemini fallback model (unlimited!)
        fallback_model = self.get_model_for_task(task_type, is_critical)
        logger.info(f"⚡ Using unlimited Gemini model {fallback_model} for {task_type}")
        return await llm_call(model=fallback_model, **kwargs)

# Global instance
_hybrid_manager = None

def get_hybrid_manager() -> HybridModelManager:
    """Get the global hybrid model manager."""
    global _hybrid_manager
    if _hybrid_manager is None:
        _hybrid_manager = HybridModelManager()
        logger.info("🚀 Initialized hybrid model manager with rate limiting")
    return _hybrid_manager

def reset_hybrid_manager():
    """Reset the global hybrid model manager (useful for testing or server restarts)."""
    global _hybrid_manager
    _hybrid_manager = None
    logger.info("🔄 Reset hybrid model manager")

async def execute_llm_with_rate_limiting(llm_call: Callable[..., Awaitable[Any]], 
                                       task_type: str = "unknown",
                                       is_critical: bool = False,
                                       **kwargs) -> Any:
    """Convenience function for rate-limited LLM execution."""
    manager = get_hybrid_manager()
    return await manager.execute_with_rate_limiting(llm_call, task_type, is_critical, **kwargs)
